#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Optimierte Version des Fruit Ninja Spiels, die nur die Handpositionen verwendet.
Liest aus dem hand_shm Shared Memory und sendet die Daten über Socket.IO.
"""

import eventlet
eventlet.monkey_patch()

from flask import Flask, send_from_directory
from flask_socketio import SocketIO
import numpy as np
import threading
import pathlib
import sys

# Import shared memory functions für Hände
from hand_shared_memory import open_hand_shm, HAND_SHM_NAME

# Pfade
BASE_DIR = pathlib.Path(__file__).parent
STATIC_DIR = BASE_DIR / "static"

# Flask-App und Socket.IO erstellen - optimiert
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret!'
socketio = SocketIO(
    app,
    async_mode="eventlet",
    cors_allowed_origins="*",
    logger=False,
    engineio_logger=False
)

# Verbindung zum Shared Memory herstellen
try:
    print(f"[Server] Verbinde mit Shared Memory '{HAND_SHM_NAME}'...")
    shm, hands = open_hand_shm()
    MAX_PERSONS = hands.shape[0]
    print(f"[Server] Verbunden mit Shared Memory, Shape: {hands.shape}")
except FileNotFoundError:
    print(f"[Server] FEHLER: Shared Memory '{HAND_SHM_NAME}' nicht gefunden!")
    print(f"[Server] Stellen Sie sicher, dass der Writer-Prozess zuerst gestartet wird.")
    sys.exit(1)

# Socket.IO Event-Handler
@socketio.on('connect', namespace='/hands')
def handle_connect():
    print('[Socket.IO] Client verbunden mit /hands Namespace')

@socketio.on('disconnect', namespace='/hands')
def handle_disconnect():
    print('[Socket.IO] Client getrennt von /hands Namespace')

# Broadcaster-Thread - optimiert für Raspberry Pi
def broadcast_loop():
    """Liest aus dem Shared Memory und sendet über Socket.IO"""
    frame_count = 0
    # Buffer für bessere Performance vorallokieren
    buf = np.empty((MAX_PERSONS, 2, 3), dtype=np.float32)
    
    while True:
        try:
            # Daten in den Buffer kopieren
            np.copyto(buf, hands)
            
            # Gültige Personen prüfen (Konfidenz > 0 für mindestens eine Hand)
            valid_mask = np.logical_or(buf[:, 0, 2] > 0, buf[:, 1, 2] > 0)
            valid_count = int(valid_mask.sum())
            
            if valid_count > 0:
                # Daten für gültige Personen extrahieren
                valid_persons = []
                for idx in range(MAX_PERSONS):
                    if buf[idx, 0, 2] > 0 or buf[idx, 1, 2] > 0:  # Wenn mindestens eine Hand sichtbar ist
                        person_data = {
                            "id": idx,
                            "hands": [
                                buf[idx, 0].tolist(),  # Linke Hand
                                buf[idx, 1].tolist()   # Rechte Hand
                            ]
                        }
                        valid_persons.append(person_data)
                
                # Über Socket.IO senden
                socketio.emit("hands", valid_persons, namespace="/hands")
            
            frame_count += 1
            if frame_count % 100 == 0:  # Weniger häufig loggen
                print(f"[Socket] Frame {frame_count}: {valid_count} Personen gesendet")
            
            eventlet.sleep(0.05)  # 20 FPS für bessere Performance
        except Exception as e:
            print(f"[Socket] Fehler im broadcast_loop: {e}")
            eventlet.sleep(1.0)  # Bei Fehler länger warten

# Thread starten
threading.Thread(target=broadcast_loop, daemon=True).start()

# HTTP-Routen
@app.route("/")
def root():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Hand Ninja Game - Optimiert</title>
        <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
        <link rel="icon" href="data:,">
        <style>
            body { 
                margin: 0; 
                background: #111; 
                display: flex; 
                justify-content: center; 
                align-items: center; 
                height: 100vh; 
                flex-direction: column;
                font-family: Arial, sans-serif;
            }
            canvas { 
                background: #222; 
                margin-bottom: 20px; 
                border: 2px solid #444;
            }
            #status { 
                color: white; 
                font-family: Arial, sans-serif; 
                margin-bottom: 10px; 
            }
            #score {
                color: white;
                font-size: 24px;
                margin-bottom: 10px;
            }
            #debug { 
                color: #999; 
                font-family: monospace; 
                font-size: 12px; 
                margin-top: 10px; 
                max-width: 640px; 
                overflow: auto; 
                height: 100px; 
            }
            .game-over {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
                display: none;
            }
            .game-over button {
                background: #4CAF50;
                border: none;
                color: white;
                padding: 10px 20px;
                text-align: center;
                text-decoration: none;
                display: inline-block;
                font-size: 16px;
                margin: 10px 2px;
                cursor: pointer;
                border-radius: 5px;
            }
            .instructions {
                color: white;
                margin-bottom: 20px;
                text-align: center;
            }
        </style>
    </head>
    <body>
        <div id="status">Verbinde...</div>
        <div class="instructions">
            <h2>Hand Ninja</h2>
            <p>Benutze deine rechte Hand, um die Früchte zu zerschneiden! Vermeide die Bomben!</p>
        </div>
        <div id="score">Punkte: 0</div>
        <canvas id="gameCanvas" width="320" height="240" style="width:640px; height:480px;"></canvas>
        <div id="debug"></div>
        <div class="game-over" id="gameOver">
            <h2>Game Over!</h2>
            <p id="finalScore">Deine Punkte: 0</p>
            <button onclick="restartGame()">Nochmal spielen</button>
        </div>
        
        <script>
            // Game Canvas Setup - Optimiert
            const canvas = document.getElementById("gameCanvas");
            const ctx = canvas.getContext("2d");
            const W = canvas.width, H = canvas.height;
            // Bildglättung deaktivieren für bessere Performance
            ctx.imageSmoothingEnabled = false;
            const statusEl = document.getElementById("status");
            const scoreEl = document.getElementById("score");
            const debugEl = document.getElementById("debug");
            const gameOverEl = document.getElementById("gameOver");
            const finalScoreEl = document.getElementById("finalScore");
            
            // Indizes für die Hände
            const LEFT_HAND = 0;
            const RIGHT_HAND = 1;
            
            // Spielvariablen
            let score = 0;
            let lives = 3;
            let gameActive = true;
            let fruits = [];
            let slices = [];
            
            // Handpositionen
            let leftHandPosition = null;
            let rightHandPosition = null;
            let lastLeftHandPosition = null;
            let lastRightHandPosition = null;
            
            // Fruchtbilder
            const fruitTypes = ['apple'];  // Nur Apfel verwenden
            const fruitImages = {};
            
            // Apfelbild vorladen
            const appleImg = new Image();
            appleImg.src = "/apple.png";  // Wird von Flask bereitgestellt
            fruitImages['apple'] = appleImg;
            
            // In Konsole und Debug-Element loggen
            function log(msg) {
                console.log(msg);
                debugEl.textContent += msg + "\\n";
                if (debugEl.textContent.split("\\n").length > 10) {
                    const lines = debugEl.textContent.split("\\n");
                    debugEl.textContent = lines.slice(lines.length - 10).join("\\n");
                }
                debugEl.scrollTop = debugEl.scrollHeight;
            }
            
            // Socket.IO-Verbindung erstellen - optimiert
            log("Socket.IO-Verbindung wird hergestellt...");
            const socket = io("/hands", {
                transports: ["websocket"],  // Nur WebSocket erzwingen
                upgrade: false,             // Transport-Upgrades deaktivieren
                reconnectionDelay: 100,     // Schnellere Wiederverbindung
                timeout: 5000,              // Kürzeres Timeout
                forceNew: true,             // Neue Verbindung erzwingen
                perMessageDeflate: false    // Kompression für niedrigere Latenz deaktivieren
            });
            
            socket.on("connect", () => {
                log("Socket verbunden mit ID: " + socket.id);
                statusEl.textContent = "Verbunden";
                statusEl.style.color = "green";
            });
            
            socket.on("connect_error", (error) => {
                log("Verbindungsfehler: " + error);
                statusEl.textContent = "Verbindungsfehler";
                statusEl.style.color = "red";
            });
            
            socket.on("disconnect", (reason) => {
                log("Socket getrennt, Grund: " + reason);
                statusEl.textContent = "Getrennt";
                statusEl.style.color = "red";
            });
            
            // Handdaten verarbeiten - mit Fehlerbehandlung
            socket.on("hands", data => {
                if (!gameActive) return;
                
                if (data.length > 0) {
                    // Erste Person nehmen
                    const person = data[0];
                    const hands = person.hands;
                    
                    // Sicherstellen, dass wir alle benötigten Daten haben
                    if (!hands || hands.length < 2) {
                        console.error("Unvollständige Handdaten empfangen:", hands);
                        return;
                    }
                    
                    // Letzte Positionen speichern für Bewegungserkennung
                    lastLeftHandPosition = leftHandPosition;
                    lastRightHandPosition = rightHandPosition;
                    
                    // Linke Hand verarbeiten
                    if (hands[LEFT_HAND][2] > 0) {
                        leftHandPosition = {
                            x: hands[LEFT_HAND][0] * W,
                            y: hands[LEFT_HAND][1] * H
                        };
                        
                        // Kollisionen prüfen, wenn wir eine Bewegung haben
                        if (lastLeftHandPosition) {
                            checkHandCollisions(
                                leftHandPosition,
                                lastLeftHandPosition,
                                "left"
                            );
                        }
                    } else {
                        leftHandPosition = null;
                    }
                    
                    // Rechte Hand verarbeiten
                    if (hands[RIGHT_HAND][2] > 0) {
                        rightHandPosition = {
                            x: hands[RIGHT_HAND][0] * W,
                            y: hands[RIGHT_HAND][1] * H
                        };
                        
                        // Kollisionen prüfen, wenn wir eine Bewegung haben
                        if (lastRightHandPosition) {
                            checkHandCollisions(
                                rightHandPosition,
                                lastRightHandPosition,
                                "right"
                            );
                        }
                    } else {
                        rightHandPosition = null;
                    }
                }
            });
            
            // Spielfunktionen
            function createFruit() {
                if (!gameActive) return;
                
                const isBomb = Math.random() < 0.1;  // 10% Chance für eine Bombe
                
                const fruit = {
                    x: Math.random() * (W - 100) + 50,  // Von den Rändern fernhalten
                    y: -50,  // Über dem Bildschirm starten
                    type: isBomb ? 'bomb' : 'apple',
                    radius: isBomb ? 30 : 40,
                    velocityX: (Math.random() - 0.5) * 3,  // Langsamere horizontale Bewegung
                    velocityY: 2 + Math.random() * 3,      // Langsameres Fallen
                    rotation: Math.random() * 360,
                    rotationSpeed: (Math.random() - 0.5) * 5,
                    sliced: false,
                    sliceTime: 0,
                    gravity: 0.2  // Niedrigere Schwerkraft für langsameren Fall
                };
                
                fruits.push(fruit);
                
                // Nächste Frucht planen
                const delay = 1000 + Math.random() * 1500;  // Längere Verzögerung zwischen Früchten
                setTimeout(createFruit, delay);
            }
            
            // Prüfen, ob eine Hand mit Früchten kollidiert
            function checkHandCollisions(handPos, lastHandPos, handType) {
                // Nur fortfahren, wenn wir eine signifikante Bewegung haben
                const dx = handPos.x - lastHandPos.x;
                const dy = handPos.y - lastHandPos.y;
                const distance = Math.sqrt(dx*dx + dy*dy);
                
                // Ignorieren, wenn die Bewegung zu klein ist
                if (distance < 5) return;
                
                fruits.forEach(fruit => {
                    if (fruit.sliced) return;
                    
                    // Kollision mit der Handbewegungslinie prüfen
                    const distToLine = distanceToLineSegment(
                        fruit.x, fruit.y,
                        lastHandPos.x, lastHandPos.y,
                        handPos.x, handPos.y
                    );
                    
                    // Wenn die Hand nahe genug an der Frucht ist
                    if (distToLine < fruit.radius + 10) {
                        // Frucht zerschneiden
                        fruit.sliced = true;
                        fruit.sliceTime = Date.now();
                        
                        // Schnitteffekt am nächsten Punkt auf der Linie hinzufügen
                        const closest = closestPointOnLine(
                            fruit.x, fruit.y,
                            lastHandPos.x, lastHandPos.y,
                            handPos.x, handPos.y
                        );
                        
                        // Schnitteffekt hinzufügen
                        slices.push({
                            x1: closest.x - 30, y1: closest.y - 30,
                            x2: closest.x + 30, y2: closest.y + 30,
                            time: Date.now(),
                            color: handType === "left" ? "#f55" : "#5f5" // Rot für links, Grün für rechts
                        });
                        
                        // Punkte aktualisieren
                        if (fruit.type === 'bomb') {
                            lives--;
                            if (lives <= 0) {
                                gameOver();
                            }
                        } else {
                            score += 10;
                            scoreEl.textContent = "Punkte: " + score;
                        }
                    }
                });
            }
            
            function gameOver() {
                gameActive = false;
                finalScoreEl.textContent = "Deine Punkte: " + score;
                gameOverEl.style.display = "block";
            }
            
            function restartGame() {
                score = 0;
                lives = 3;
                fruits = [];
                slices = [];
                gameActive = true;
                scoreEl.textContent = "Punkte: " + score;
                gameOverEl.style.display = "none";
                createFruit();
            }
            
            // Update-Funktion optimieren
            function update() {
                // Nächsten Frame sofort anfordern für flüssigere Animation
                requestAnimationFrame(update);
                
                // Canvas leeren
                ctx.clearRect(0, 0, W, H);
                
                // Hintergrund zeichnen
                ctx.fillStyle = "#222";
                ctx.fillRect(0, 0, W, H);
                
                // Hände zeichnen
                if (leftHandPosition) {
                    // Linke Hand zeichnen
                    ctx.fillStyle = "#f55";  // Rot für linke Hand
                    ctx.beginPath();
                    ctx.arc(leftHandPosition.x, leftHandPosition.y, 15, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // Bewegungslinie zeichnen, wenn wir eine vorherige Position haben
                    if (lastLeftHandPosition) {
                        ctx.strokeStyle = "#f55";
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(lastLeftHandPosition.x, lastLeftHandPosition.y);
                        ctx.lineTo(leftHandPosition.x, leftHandPosition.y);
                        ctx.stroke();
                    }
                }
                
                if (rightHandPosition) {
                    // Rechte Hand zeichnen
                    ctx.fillStyle = "#5f5";  // Grün für rechte Hand
                    ctx.beginPath();
                    ctx.arc(rightHandPosition.x, rightHandPosition.y, 15, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // Bewegungslinie zeichnen, wenn wir eine vorherige Position haben
                    if (lastRightHandPosition) {
                        ctx.strokeStyle = "#5f5";
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(lastRightHandPosition.x, lastRightHandPosition.y);
                        ctx.lineTo(rightHandPosition.x, rightHandPosition.y);
                        ctx.stroke();
                    }
                }
                
                // Früchte aktualisieren und zeichnen
                for (let i = fruits.length - 1; i >= 0; i--) {
                    const fruit = fruits[i];
                    
                    // Position aktualisieren
                    fruit.x += fruit.velocityX;
                    fruit.y += fruit.velocityY;
                    fruit.velocityY += fruit.gravity;
                    fruit.rotation += fruit.rotationSpeed;
                    
                    // Entfernen, wenn außerhalb des Bildschirms
                    if (fruit.y > H + 100) {
                        if (!fruit.sliced && fruit.type !== 'bomb') {
                            lives--;
                            if (lives <= 0) {
                                gameOver();
                            }
                        }
                        fruits.splice(i, 1);
                        continue;
                    }
                    
                    // Frucht zeichnen
                    ctx.save();
                    ctx.translate(fruit.x, fruit.y);
                    ctx.rotate(fruit.rotation * Math.PI / 180);
                    
                    if (fruit.type === 'bomb') {
                        // Bombe als schwarzes Quadrat zeichnen
                        ctx.fillStyle = "#000";
                        ctx.fillRect(-fruit.radius, -fruit.radius, fruit.radius * 2, fruit.radius * 2);
                        
                        // Zündschnur hinzufügen
                        ctx.strokeStyle = "#f80";
                        ctx.lineWidth = 3;
                        ctx.beginPath();
                        ctx.moveTo(0, -fruit.radius);
                        ctx.lineTo(0, -fruit.radius - 15);
                        ctx.stroke();
                        
                        // Funken hinzufügen
                        ctx.fillStyle = "#ff0";
                        ctx.beginPath();
                        ctx.arc(0, -fruit.radius - 15, 5, 0, Math.PI * 2);
                        ctx.fill();
                    } else {
                        // Apfel zeichnen
                        if (!fruit.sliced) {
                            ctx.drawImage(appleImg, -fruit.radius, -fruit.radius, fruit.radius * 2, fruit.radius * 2);
                        } else {
                            // Für geschnittenen Apfel zwei Kopien mit Versatz zeichnen
                            const timeSinceSlice = Date.now() - fruit.sliceTime;
                            const offset = timeSinceSlice * 0.1;
                            
                            ctx.save();
                            ctx.translate(-offset, 0);
                            ctx.drawImage(appleImg, -fruit.radius, -fruit.radius, fruit.radius, fruit.radius * 2);
                            ctx.restore();
                            
                            ctx.save();
                            ctx.translate(offset, 0);
                            ctx.drawImage(appleImg, 0, -fruit.radius, fruit.radius, fruit.radius * 2);
                            ctx.restore();
                        }
                    }
                    
                    ctx.restore();
                }
                
                // Schnitte zeichnen
                for (let i = slices.length - 1; i >= 0; i--) {
                    const slice = slices[i];
                    const age = Date.now() - slice.time;
                    
                    if (age > 500) {
                        slices.splice(i, 1);
                        continue;
                    }
                    
                    const alpha = 1 - age / 500;
                    // Farbe aus dem Slice-Objekt verwenden oder Standard-Weiß
                    const color = slice.color || "#fff";
                    const rgbValues = color.match(/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
                    let rgbColor = "255,255,255"; // Standard-Weiß
                    if (rgbValues) {
                        rgbColor = parseInt(rgbValues[1], 16) + "," + 
                                  parseInt(rgbValues[2], 16) + "," + 
                                  parseInt(rgbValues[3], 16);
                    }
                    
                    ctx.strokeStyle = "rgba(" + rgbColor + ", " + alpha + ")";
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(slice.x1, slice.y1);
                    ctx.lineTo(slice.x2, slice.y2);
                    ctx.stroke();
                }
                
                // Leben zeichnen
                ctx.fillStyle = "#f00";
                for (let i = 0; i < lives; i++) {
                    ctx.beginPath();
                    ctx.arc(30 + i * 30, 30, 10, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
            
            // Spiel starten
            createFruit();
            update();
            
            // Zusätzliches Debugging
            socket.io.on("error", (error) => {
                log("Transport-Fehler: " + error);
            });
            
            socket.io.on("reconnect_attempt", (attempt) => {
                log("Wiederverbindungsversuch: " + attempt);
            });
            
            // Hilfsfunktion zur Berechnung des Abstands von einem Punkt zu einer Linie
            function distanceToLineSegment(px, py, x1, y1, x2, y2) {
                const A = px - x1;
                const B = py - y1;
                const C = x2 - x1;
                const D = y2 - y1;
                
                const dot = A * C + B * D;
                const len_sq = C * C + D * D;
                let param = -1;
                
                if (len_sq !== 0) {
                    param = dot / len_sq;
                }
                
                let xx, yy;
                
                if (param < 0) {
                    xx = x1;
                    yy = y1;
                } else if (param > 1) {
                    xx = x2;
                    yy = y2;
                } else {
                    xx = x1 + param * C;
                    yy = y1 + param * D;
                }
                
                const dx = px - xx;
                const dy = py - yy;
                
                return Math.sqrt(dx * dx + dy * dy);
            }
            
            // Hilfsfunktion zum Finden des nächsten Punkts auf einer Linie
            function closestPointOnLine(px, py, x1, y1, x2, y2) {
                const A = px - x1;
                const B = py - y1;
                const C = x2 - x1;
                const D = y2 - y1;
                
                const dot = A * C + B * D;
                const len_sq = C * C + D * D;
                let param = -1;
                
                if (len_sq !== 0) {
                    param = dot / len_sq;
                }
                
                if (param < 0) {
                    return {x: x1, y: y1};
                } else if (param > 1) {
                    return {x: x2, y: y2};
                } else {
                    return {
                        x: x1 + param * C,
                        y: y1 + param * D
                    };
                }
            }
        </script>
    </body>
    </html>
    """

@app.route("/<path:filename>")
def static_file(filename):
    # Zuerst versuchen, aus dem static-Verzeichnis zu bedienen
    try:
        return send_from_directory(STATIC_DIR, filename)
    except:
        # Wenn nicht gefunden, versuchen, aus dem Bildverzeichnis zu bedienen
        image_dir = pathlib.Path("/home/<USER>/Documents/taha_ai/hrmn")
        return send_from_directory(image_dir, filename)

if __name__ == "__main__":
    print(f"[Server] Starte Hand Ninja Game Server auf http://0.0.0.0:5000")
    print(f"[Server] OPTIMIERTE VERSION für Raspberry Pi 5 - NUR HÄNDE")
    try:
        # Debug-Modus für bessere Performance ausschalten
        socketio.run(app, host="0.0.0.0", port=5000, debug=False)
    except Exception as e:
        print(f"[Server] Fehler beim Starten des Servers: {e}")
    finally:
        shm.close()
