#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Template für ein neues Spiel mit Pose-Erkennung
Basiert auf simple_pose_socket.py Architektur
"""

import eventlet
eventlet.monkey_patch()

from flask import Flask, send_from_directory
from flask_socketio import SocketIO
import numpy as np
import time
import threading
import pathlib
import sys

# Import shared memory functions
from shared_memory import open_pose_shm, POSE_SHM_NAME

# Paths
BASE_DIR = pathlib.Path(__file__).parent
STATIC_DIR = BASE_DIR / "static"

# Create Flask app and Socket.IO
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret!'
socketio = SocketIO(
    app,
    async_mode="eventlet",
    cors_allowed_origins="*",
    logger=False,
    engineio_logger=False)

# Connect to shared memory
try:
    print(f"[Server] Connecting to shared memory '{POSE_SHM_NAME}'...")
    shm, poses = open_pose_shm()
    MAX_PERSONS, NUM_KPTS = poses.shape[:2]
    print(f"[Server] Connected to shared memory with shape {poses.shape}")
except FileNotFoundError:
    print(f"[Server] ERROR: Shared memory '{POSE_SHM_NAME}' not found!")
    print(f"[Server] Make sure the writer process is running first.")
    sys.exit(1)

# Socket.IO event handlers
@socketio.on('connect', namespace='/pose')
def handle_connect():
    print('[Socket.IO] Client connected to /pose namespace')

@socketio.on('disconnect', namespace='/pose')
def handle_disconnect():
    print('[Socket.IO] Client disconnected from /pose namespace')

# Broadcaster thread
def broadcast_loop():
    """Read from shared memory and broadcast via Socket.IO"""
    frame_count = 0
    buf = np.empty((MAX_PERSONS, NUM_KPTS, 3), dtype=np.float32)
    
    # Keypoint indices we care about
    keypoint_indices = [0, 5, 7, 9, 6, 8, 10]  # nose, left/right shoulder, elbow, wrist
    
    while True:
        try:
            np.copyto(buf, poses)
            valid_mask = (buf[:, 0, 2] > 0)
            valid_count = int(valid_mask.sum())
            
            if valid_count > 0:
                valid_persons = []
                for idx in range(MAX_PERSONS):
                    if buf[idx, 0, 2] > 0:  # If nose confidence > 0
                        person_data = {
                            "id": idx,
                            "kpts": [buf[idx, kp_idx].tolist() for kp_idx in keypoint_indices]
                        }
                        valid_persons.append(person_data)
                
                socketio.emit("pose", valid_persons, namespace="/pose")
            
            frame_count += 1
            if frame_count % 60 == 0:  # Log every 60 frames
                print(f"[Broadcast] Frame {frame_count}: {valid_count} persons detected")
            
        except Exception as e:
            print(f"[Broadcast] Error: {e}")
        
        eventlet.sleep(0.033)  # ~30 FPS

# Start broadcaster thread
threading.Thread(target=broadcast_loop, daemon=True).start()

# HTTP routes
@app.route("/")
def root():
    return send_from_directory(STATIC_DIR, "my_new_game.html")

@app.route("/<path:filename>")
def static_file(filename):
    return send_from_directory(STATIC_DIR, filename)

if __name__ == "__main__":
    print("[Server] Starting My New Game server on http://localhost:5000")
    socketio.run(app, host="0.0.0.0", port=5000, debug=False)
