#!/bin/bash

# Video Sink Switcher Script
# This script allows you to easily switch between autovideosink and fakesink modes

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/video_sink_config.conf"

# Function to show current configuration
show_current() {
    if [ -f "$CONFIG_FILE" ]; then
        source "$CONFIG_FILE"
        echo "Current video sink mode: $VIDEO_SINK"
        if [ "$VIDEO_SINK" = "fakesink" ]; then
            echo "  → Performance mode (no video display)"
        else
            echo "  → Display mode (shows video output)"
        fi
    else
        echo "No configuration file found. Default: autovideosink (display mode)"
    fi
}

# Function to set video sink mode
set_mode() {
    local mode="$1"
    echo "# Global Video Sink Configuration" > "$CONFIG_FILE"
    echo "# This file controls the video output behavior for all Hailo pose estimation scripts" >> "$CONFIG_FILE"
    echo "#" >> "$CONFIG_FILE"
    echo "# Available options:" >> "$CONFIG_FILE"
    echo "#   VIDEO_SINK=\"autovideosink\"  - Show video output (default, good for debugging)" >> "$CONFIG_FILE"
    echo "#   VIDEO_SINK=\"fakesink\"       - No video output (performance mode, good for production)" >> "$CONFIG_FILE"
    echo "#" >> "$CONFIG_FILE"
    echo "# Change this value to switch between display and performance modes globally" >> "$CONFIG_FILE"
    echo "" >> "$CONFIG_FILE"
    echo "VIDEO_SINK=\"$mode\"" >> "$CONFIG_FILE"
    
    echo "✅ Video sink mode set to: $mode"
    if [ "$mode" = "fakesink" ]; then
        echo "  → Performance mode activated (no video display)"
        echo "  → Better performance, lower CPU usage"
    else
        echo "  → Display mode activated (shows video output)"
        echo "  → Good for debugging and visual feedback"
    fi
    echo ""
    echo "⚠️  Note: Restart any running pose estimation processes for changes to take effect."
}

# Main script logic
case "$1" in
    "performance"|"fakesink"|"perf")
        set_mode "fakesink"
        ;;
    "display"|"autovideosink"|"auto"|"show")
        set_mode "autovideosink"
        ;;
    "status"|"current"|"")
        show_current
        ;;
    "help"|"-h"|"--help")
        echo "Video Sink Switcher - Global configuration for Hailo pose estimation"
        echo ""
        echo "Usage: $0 [MODE]"
        echo ""
        echo "Modes:"
        echo "  performance, fakesink, perf     - Enable performance mode (no video display)"
        echo "  display, autovideosink, show    - Enable display mode (show video output)"
        echo "  status, current, (no argument)  - Show current configuration"
        echo "  help, -h, --help               - Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0 performance    # Switch to performance mode"
        echo "  $0 display        # Switch to display mode"
        echo "  $0                # Show current mode"
        echo ""
        echo "Note: You need to restart pose estimation processes for changes to take effect."
        ;;
    *)
        echo "❌ Unknown mode: $1"
        echo "Use '$0 help' for available options."
        exit 1
        ;;
esac
