#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, jsonify
from shared_memory import init_pose_shm, POSE_SHM_NAME

# These must match the same values in pose_estimation.py
MAX_PERSONS = 10
NUM_KPTS    = 17

# 1) Attach to the *same* shared memory
pose_shm, pose_arr = init_pose_shm(MAX_PERSONS, NUM_KPTS)

# 2) Create the Flask app
app = Flask(__name__)

@app.route("/")
def index():
    return "Pose Estimation Server is running. Hit /pose for coordinates."

@app.route("/pose")
def get_pose_data():
    """
    Return the pose array as JSON.
    shape = (MAX_PERSONS, NUM_KPTS, 3)
    Each 'row' corresponds to one person:
    [
       [[x1, y1, conf1], [x2, y2, conf2], ... up to 17],
       ...
       up to 10 persons
    ]
    """
    # Convert the shared memory array (numpy) into a Python list
    data = pose_arr.tolist()
    return jsonify(data)

if __name__ == "__main__":
    # Run Flask on 0.0.0.0:5000 (reachable on your LAN)
    app.run(host="0.0.0.0", port=5000, debug=False)
