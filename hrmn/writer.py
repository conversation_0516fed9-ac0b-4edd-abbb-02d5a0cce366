# mock_writer.py

import time
import numpy as np
from shared_memory import init_pose_shm

MAX_PERSONS = 10
NUM_KPTS = 17

# The "writer" side: create & fill
shm, pose_arr = init_pose_shm(MAX_PERSONS, NUM_KPTS)
print("Mock writer started. Writing random coordinates every 1 second...")

try:
    while True:
        pose_arr.fill(-1.)
        # Let's pretend we detect 1 person
        for kp in range(NUM_KPTS):
            x = np.random.rand()
            y = np.random.rand()
            conf = 0.8 + 0.2*np.random.rand()  # e.g. 0.80-1.00
            pose_arr[0, kp] = (x, y, conf)

        time.sleep(1)

except KeyboardInterrupt:
    print("Mock writer shutting down")
finally:
    shm.close()
    shm.unlink()
