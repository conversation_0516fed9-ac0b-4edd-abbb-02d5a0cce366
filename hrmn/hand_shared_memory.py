#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
from multiprocessing import shared_memory

HAND_SHM_NAME = "hand_shm"

def init_hand_shm(max_persons=10):
    """Erzeugt /hand_shm neu (vorheriges unlink).
    Speichert nur die Handpositionen für jede Person.
    Format: (max_persons, 2, 3) - 2 <PERSON><PERSON>nde (links/rechts), 3 Werte (x, y, conf)
    """
    nbytes = max_persons * 2 * 3 * np.float32().nbytes
    try:
        old = shared_memory.SharedMemory(name=HAND_SHM_NAME)
        old.unlink(); old.close()
    except FileNotFoundError:
        pass

    shm = shared_memory.SharedMemory(create=True, size=nbytes, name=HAND_SHM_NAME)
    arr = np.ndarray((max_persons, 2, 3), dtype=np.float32, buffer=shm.buf)
    arr.fill(-1.0)
    return shm, arr

def open_hand_shm(max_persons=10):
    """Öffnet /hand_shm (<PERSON><PERSON>, falls nicht existiert)."""
    shm = shared_memory.SharedMemory(name=HAND_SHM_NAME, create=False)
    arr = np.ndarray((max_persons, 2, 3), dtype=np.float32, buffer=shm.buf)
    return shm, arr
