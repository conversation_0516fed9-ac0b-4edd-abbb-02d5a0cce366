#!/bin/bash

# Source the setup_env.sh file to set the TAPPAS_POST_PROC_DIR environment variable
if [ -f "/home/<USER>/Documents/taha_ai/hailo-rpi5-examples/setup_env.sh" ]; then
    echo "Sourcing setup_env.sh from hailo-rpi5-examples..."
    source /home/<USER>/Documents/taha_ai/hailo-rpi5-examples/setup_env.sh
elif [ -f "/home/<USER>/Documents/taha_ai/venv_hailo/setup_env.sh" ]; then
    echo "Sourcing setup_env.sh from venv_hailo..."
    source /home/<USER>/Documents/taha_ai/venv_hailo/setup_env.sh
else
    echo "Error: Could not find setup_env.sh file."
    exit 1
fi

# Load global video sink configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/video_sink_config.conf"

# Default to autovideosink if config file doesn't exist
VIDEO_SINK="autovideosink"

if [ -f "$CONFIG_FILE" ]; then
    source "$CONFIG_FILE"
    echo "[CONFIG] Using video sink: $VIDEO_SINK"
else
    echo "[CONFIG] No config file found, using default: $VIDEO_SINK"
fi

# Set GStreamer environment variable based on configuration
if [ "$VIDEO_SINK" = "fakesink" ]; then
    export GST_AUTOVIDEOSINK=fakesink
    echo "[CONFIG] Set GST_AUTOVIDEOSINK=fakesink for performance mode"
else
    unset GST_AUTOVIDEOSINK
    echo "[CONFIG] Using autovideosink for display mode"
fi

# Run the pose_estimation.py script with the provided arguments
python pose_estimation.py "$@"
