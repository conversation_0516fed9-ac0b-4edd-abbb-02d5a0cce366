#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple Flask app to test hand pose detection latency.
Reads from the original pose shared memory and extracts hand positions.
"""

from flask import Flask, jsonify
import json
import time
import threading
from shared_memory import open_pose_shm, POSE_SHM_NAME
import sys

app = Flask(__name__)

# Global variables to store hand data
current_hand_data = {
    "timestamp": 0,
    "persons": [],
    "fps": 0,
    "frame_count": 0
}

# Hand indices in the original pose array
LEFT_WRIST_IDX = 9
RIGHT_WRIST_IDX = 10

# Try to connect to shared memory
try:
    print(f"[Server] Connecting to shared memory '{POSE_SHM_NAME}'...")
    shm, poses = open_pose_shm()
    MAX_PERSONS, NUM_KPTS = poses.shape[:2]
    print(f"[Server] Connected to shared memory with shape: {poses.shape}")
except FileNotFoundError:
    print(f"[Server] ERROR: Shared memory '{POSE_SHM_NAME}' not found!")
    print(f"[Server] Make sure the pose estimation is running first.")
    sys.exit(1)

def read_hand_data():
    """Continuously read hand data from shared memory"""
    global current_hand_data
    frame_count = 0
    last_time = time.time()
    
    while True:
        try:
            # Read current pose positions
            snap = poses.copy()
            current_time = time.time()
            
            # Extract valid persons with hand data
            persons = []
            for idx in range(MAX_PERSONS):
                # Check if person exists (nose confidence > 0)
                if snap[idx, 0, 2] > 0:
                    # Extract hand positions
                    left_hand = snap[idx, LEFT_WRIST_IDX]  # [x, y, confidence]
                    right_hand = snap[idx, RIGHT_WRIST_IDX]  # [x, y, confidence]
                    
                    person_data = {
                        "id": idx,
                        "left_hand": {
                            "x": float(left_hand[0]),
                            "y": float(left_hand[1]),
                            "confidence": float(left_hand[2]),
                            "visible": left_hand[2] > 0
                        },
                        "right_hand": {
                            "x": float(right_hand[0]),
                            "y": float(right_hand[1]),
                            "confidence": float(right_hand[2]),
                            "visible": right_hand[2] > 0
                        }
                    }
                    persons.append(person_data)
            
            # Calculate FPS
            frame_count += 1
            if current_time - last_time >= 1.0:
                fps = frame_count / (current_time - last_time)
                current_hand_data["fps"] = round(fps, 1)
                frame_count = 0
                last_time = current_time
            
            # Update global data
            current_hand_data.update({
                "timestamp": current_time * 1000,  # Convert to milliseconds
                "persons": persons,
                "frame_count": current_hand_data["frame_count"] + 1
            })
            
            time.sleep(0.033)  # ~30 FPS
            
        except Exception as e:
            print(f"[Error] Reading hand data: {e}")
            time.sleep(1.0)

# Start background thread to read hand data
threading.Thread(target=read_hand_data, daemon=True).start()

@app.route("/")
def index():
    return """
<!DOCTYPE html>
<html>
<head>
    <title>Simple Hand Latency Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #111;
            color: white;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .stats {
            background: #222;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #4CAF50;
        }
        .stat-label {
            font-size: 0.9em;
            color: #999;
        }
        .hands-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .hand-panel {
            flex: 1;
            background: #222;
            padding: 20px;
            border-radius: 10px;
        }
        .hand-title {
            font-size: 1.5em;
            margin-bottom: 15px;
            text-align: center;
        }
        .left-hand { border-left: 5px solid #f55; }
        .right-hand { border-left: 5px solid #5f5; }
        .hand-data {
            font-family: monospace;
            font-size: 1.1em;
            line-height: 1.6;
        }
        .visible { color: #4CAF50; }
        .hidden { color: #999; }
        .canvas-container {
            background: #222;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        canvas {
            border: 2px solid #444;
            background: #000;
        }
        .latency-test {
            background: #333;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: center;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .latency-result {
            font-size: 1.5em;
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
        }
        .good-latency { background: #4CAF50; }
        .medium-latency { background: #ff9800; }
        .bad-latency { background: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Hand Latency Test</h1>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="fps">0</div>
                <div class="stat-label">FPS</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="persons">0</div>
                <div class="stat-label">Persons</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="frames">0</div>
                <div class="stat-label">Frames</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="latency">-</div>
                <div class="stat-label">Latency (ms)</div>
            </div>
        </div>
        
        <div class="hands-container">
            <div class="hand-panel left-hand">
                <div class="hand-title">Left Hand</div>
                <div class="hand-data" id="left-hand-data">No data</div>
            </div>
            <div class="hand-panel right-hand">
                <div class="hand-title">Right Hand</div>
                <div class="hand-data" id="right-hand-data">No data</div>
            </div>
        </div>
        
        <div class="canvas-container">
            <h3>Visual Hand Tracking</h3>
            <canvas id="handCanvas" width="640" height="480"></canvas>
        </div>
        
        <div class="latency-test">
            <h3>Latency Test</h3>
            <p>Click the button and immediately move your hand to test response time:</p>
            <button class="test-button" onclick="startLatencyTest()">Start Latency Test</button>
            <div id="latency-result" class="latency-result" style="display: none;"></div>
        </div>
    </div>

    <script>
        let lastUpdateTime = 0;
        let testStartTime = 0;
        let testActive = false;
        let lastHandPosition = null;
        
        // Canvas setup
        const canvas = document.getElementById('handCanvas');
        const ctx = canvas.getContext('2d');
        const W = canvas.width;
        const H = canvas.height;
        
        function updateDisplay(data) {
            // Update stats
            document.getElementById('fps').textContent = data.fps || 0;
            document.getElementById('persons').textContent = data.persons.length;
            document.getElementById('frames').textContent = data.frame_count || 0;
            
            // Calculate latency (time since last update)
            const now = Date.now();
            if (lastUpdateTime > 0) {
                const latency = now - data.timestamp;
                document.getElementById('latency').textContent = latency + 'ms';
            }
            lastUpdateTime = now;
            
            // Update hand data
            if (data.persons.length > 0) {
                const person = data.persons[0]; // Use first person
                
                // Left hand
                const leftHandEl = document.getElementById('left-hand-data');
                if (person.left_hand.visible) {
                    leftHandEl.innerHTML = `
                        <div class="visible">VISIBLE</div>
                        X: ${person.left_hand.x.toFixed(3)}<br>
                        Y: ${person.left_hand.y.toFixed(3)}<br>
                        Confidence: ${person.left_hand.confidence.toFixed(3)}
                    `;
                    leftHandEl.className = 'hand-data visible';
                } else {
                    leftHandEl.innerHTML = '<div class="hidden">NOT VISIBLE</div>';
                    leftHandEl.className = 'hand-data hidden';
                }
                
                // Right hand
                const rightHandEl = document.getElementById('right-hand-data');
                if (person.right_hand.visible) {
                    rightHandEl.innerHTML = `
                        <div class="visible">VISIBLE</div>
                        X: ${person.right_hand.x.toFixed(3)}<br>
                        Y: ${person.right_hand.y.toFixed(3)}<br>
                        Confidence: ${person.right_hand.confidence.toFixed(3)}
                    `;
                    rightHandEl.className = 'hand-data visible';
                } else {
                    rightHandEl.innerHTML = '<div class="hidden">NOT VISIBLE</div>';
                    rightHandEl.className = 'hand-data hidden';
                }
                
                // Draw on canvas
                drawHands(person);
                
                // Check for latency test
                if (testActive) {
                    checkLatencyTest(person);
                }
            } else {
                // No persons detected
                document.getElementById('left-hand-data').innerHTML = '<div class="hidden">NO PERSON DETECTED</div>';
                document.getElementById('right-hand-data').innerHTML = '<div class="hidden">NO PERSON DETECTED</div>';
                document.getElementById('left-hand-data').className = 'hand-data hidden';
                document.getElementById('right-hand-data').className = 'hand-data hidden';
                
                // Clear canvas
                ctx.clearRect(0, 0, W, H);
                ctx.fillStyle = '#333';
                ctx.fillRect(0, 0, W, H);
                ctx.fillStyle = '#999';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('No person detected', W/2, H/2);
            }
        }
        
        function drawHands(person) {
            // Clear canvas
            ctx.clearRect(0, 0, W, H);
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, W, H);
            
            // Draw left hand
            if (person.left_hand.visible) {
                const x = person.left_hand.x * W;
                const y = person.left_hand.y * H;
                
                ctx.fillStyle = '#f55';
                ctx.beginPath();
                ctx.arc(x, y, 20, 0, Math.PI * 2);
                ctx.fill();
                
                // Label
                ctx.fillStyle = '#fff';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('L', x, y + 5);
            }
            
            // Draw right hand
            if (person.right_hand.visible) {
                const x = person.right_hand.x * W;
                const y = person.right_hand.y * H;
                
                ctx.fillStyle = '#5f5';
                ctx.beginPath();
                ctx.arc(x, y, 20, 0, Math.PI * 2);
                ctx.fill();
                
                // Label
                ctx.fillStyle = '#fff';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('R', x, y + 5);
            }
        }
        
        function startLatencyTest() {
            testStartTime = Date.now();
            testActive = true;
            lastHandPosition = null;
            
            document.getElementById('latency-result').style.display = 'none';
            
            // Show instruction
            const resultEl = document.getElementById('latency-result');
            resultEl.innerHTML = 'Move your hand NOW!';
            resultEl.className = 'latency-result';
            resultEl.style.display = 'block';
            resultEl.style.background = '#2196F3';
        }
        
        function checkLatencyTest(person) {
            if (!testActive) return;
            
            // Check if any hand moved significantly
            let currentPos = null;
            if (person.right_hand.visible) {
                currentPos = {x: person.right_hand.x, y: person.right_hand.y};
            } else if (person.left_hand.visible) {
                currentPos = {x: person.left_hand.x, y: person.left_hand.y};
            }
            
            if (currentPos && lastHandPosition) {
                const dx = currentPos.x - lastHandPosition.x;
                const dy = currentPos.y - lastHandPosition.y;
                const distance = Math.sqrt(dx*dx + dy*dy);
                
                // If hand moved significantly (threshold: 0.1 normalized units)
                if (distance > 0.1) {
                    const latency = Date.now() - testStartTime;
                    testActive = false;
                    
                    const resultEl = document.getElementById('latency-result');
                    resultEl.innerHTML = `Latency: ${latency}ms`;
                    
                    if (latency < 100) {
                        resultEl.className = 'latency-result good-latency';
                    } else if (latency < 200) {
                        resultEl.className = 'latency-result medium-latency';
                    } else {
                        resultEl.className = 'latency-result bad-latency';
                    }
                }
            }
            
            lastHandPosition = currentPos;
        }
        
        // Fetch data every 33ms (~30 FPS)
        function fetchData() {
            fetch('/api/hands')
                .then(response => response.json())
                .then(data => updateDisplay(data))
                .catch(error => console.error('Error fetching data:', error));
        }
        
        // Start fetching data
        setInterval(fetchData, 33);
        fetchData(); // Initial fetch
    </script>
</body>
</html>
    """

@app.route("/api/hands")
def api_hands():
    """API endpoint to get current hand data as JSON"""
    return jsonify(current_hand_data)

if __name__ == "__main__":
    print(f"[Server] Starting Simple Hand Latency Test server on http://0.0.0.0:5001")
    print(f"[Server] Reading from shared memory: {POSE_SHM_NAME}")
    try:
        app.run(host="0.0.0.0", port=5001, debug=False, threaded=True)
    except Exception as e:
        print(f"[Server] Error starting server: {e}")
    finally:
        shm.close()
