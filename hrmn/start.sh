#!/bin/bash

# =============================================================================
# Raspberry Pi Pose Estimation & Hand Ninja Game Startup Script
# =============================================================================
# This script starts both the backend (pose estimation) and frontend (web server)
# Usage: ./start.sh [OPTIONS]
#
# Options:
#   --camera-input INPUT    Camera input (rpi, usb, /dev/videoX) [default: rpi]
#   --performance          Use fakesink (performance mode)
#   --display              Use autovideosink (display mode)
#   --stop                 Stop all running processes
#   --status               Show status of running processes
#   --help                 Show this help message
# =============================================================================

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Default settings
CAMERA_INPUT="rpi"
BACKEND_SCRIPT="./run_pose_estimation.sh"
FRONTEND_SCRIPT="python simple_pose_socket.py"
AUTO_STOP=true
OPEN_BROWSER=true

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[STARTUP]${NC} $1"
}

# Function to check if a process is running
check_process() {
    local name="$1"
    local pattern="$2"
    if pgrep -f "$pattern" > /dev/null; then
        print_status "$name is running (PID: $(pgrep -f "$pattern"))"
        return 0
    else
        print_warning "$name is not running"
        return 1
    fi
}

# Function to stop processes - AGGRESSIVE CLEANUP like SDP project
stop_processes() {
    print_header "Aggressive cleanup of all pose estimation and related processes..."

    # Wait a moment for system stability
    sleep 1

    # 1) Kill specific pose estimation processes first (without sudo for now)
    pkill -f "pose_estimation.py" && print_status "Pose estimation processes killed" || true
    pkill -f "hand_pose_estimation.py" && print_status "Hand pose estimation processes killed" || true
    pkill -f "simple_pose_socket.py" && print_status "Web server processes killed" || true
    pkill -f "hand_ninja_game.py" && print_status "Hand ninja game processes killed" || true

    # 2) Clean up /tmp (with exceptions like the SDP project) - skip for now
    print_status "Skipping /tmp cleanup (requires sudo)"

    # 3) Kill Hailo processes (with exclusions like our own scripts)
    print_status "Terminating Hailo processes..."
    HAILO_EXCLUSION="start.sh|switch_video_sink.sh|grep|hailo-tappas"  # Exclude our own scripts

    while pgrep -a -f hailo | grep -Ev "$HAILO_EXCLUSION" >/dev/null 2>&1
    do
        # List all relevant processes (with exclusions)
        pgrep -a -f hailo | grep -Ev "$HAILO_EXCLUSION" | while read -r PID LINE
        do
            # Zombie check: If process status is 'Z' → Zombie
            if ps -o stat= -p "$PID" 2>/dev/null | grep -q 'Z'; then
                print_warning "Zombie process found: $PID -> $LINE"
                # Get parent process ID
                ZPPID="$(ps -o ppid= -p "$PID" 2>/dev/null)"
                # If PPID > 1, kill the parent process (PPID=1 would be init/systemd)
                if [[ "$ZPPID" -gt 1 ]] 2>/dev/null; then
                    print_status "Killing parent process $ZPPID of zombie $PID"
                    sudo kill -9 "$ZPPID" 2>/dev/null || true
                else
                    print_warning "Parent process is $ZPPID (init?), no kill executed."
                fi
            else
                print_status "Killing $PID: $LINE"
                sudo kill -9 "$PID" 2>/dev/null || true
            fi
        done
        sleep 1
    done

    # 4) Kill any remaining pose/camera/gstreamer processes
    print_status "Killing remaining camera and GStreamer processes..."
    sudo pkill -9 -f "picamera" 2>/dev/null || true
    sudo pkill -9 -f "libcamera" 2>/dev/null || true
    sudo pkill -9 -f "gst-launch" 2>/dev/null || true
    sudo pkill -9 -f "python.*pose" 2>/dev/null || true
    sudo pkill -9 -f "multiprocessing.resource_tracker" 2>/dev/null || true

    # 5) Complete Hailo device reset (based on community solutions)
    print_status "Performing complete Hailo device reset..."

    # Stop all Hailo services
    sudo systemctl stop hailort 2>/dev/null || true
    sudo pkill -9 hailort_service 2>/dev/null || true

    # Reset camera subsystem (for "Device or resource busy" error)
    print_status "Resetting camera subsystem..."
    sudo pkill -9 -f "libcamera" 2>/dev/null || true
    sudo pkill -9 -f "rpicam" 2>/dev/null || true

    # Unload and reload camera modules
    sudo modprobe -r bcm2835_v4l2 2>/dev/null || true
    sudo modprobe -r bcm2835_mmal_vchiq 2>/dev/null || true
    sleep 2
    sudo modprobe bcm2835_v4l2 2>/dev/null || true
    sudo modprobe bcm2835_mmal_vchiq 2>/dev/null || true

    # Complete Hailo PCI module reset
    print_status "Performing complete Hailo PCI reset..."
    {
        # Force remove all Hailo modules
        sudo rmmod hailo_pci 2>/dev/null || true
        sudo modprobe -r hailo_pci 2>/dev/null || true

        # Clear any device locks
        sudo rm -f /tmp/hailo* 2>/dev/null || true
        sudo rm -f /var/lock/hailo* 2>/dev/null || true

        # Wait longer for complete device release
        sleep 5

        # Reload Hailo modules
        sudo modprobe hailo_pci 2>/dev/null || true
        sleep 3

        # Restart Hailo service
        sudo systemctl start hailort 2>/dev/null || true
        sleep 2

        print_status "Complete Hailo device reset completed"
    } || print_warning "Hailo reset failed (device may need manual intervention)"

    # 7) Clean up shared memory
    print_status "Cleaning up shared memory..."
    sudo rm -f /dev/shm/pose_shm 2>/dev/null || true
    sudo rm -f /dev/shm/hand_shm 2>/dev/null || true

    # 8) Wait longer for devices to be fully released
    print_status "Waiting for devices to be released..."
    sleep 10

    print_status "Aggressive cleanup completed - all devices should be free now"
}

# Function to show status
show_status() {
    print_header "System Status Check"
    echo ""
    
    # Check video sink configuration
    if [ -f "video_sink_config.conf" ]; then
        source video_sink_config.conf
        print_status "Video sink mode: $VIDEO_SINK"
    else
        print_warning "No video sink configuration found"
    fi
    
    echo ""
    print_header "Process Status"
    check_process "Pose Estimation Backend" "pose_estimation.py"
    check_process "Hand Pose Estimation Backend" "hand_pose_estimation.py"
    check_process "Web Server Frontend" "simple_pose_socket.py"
    check_process "Hand Ninja Game Server" "hand_ninja_game.py"
    
    echo ""
    print_header "Shared Memory Status"
    if [ -f "/dev/shm/pose_shm" ]; then
        print_status "Shared memory 'pose_shm' exists"
    else
        print_warning "Shared memory 'pose_shm' not found"
    fi
    
    echo ""
    print_header "Camera Status"
    if [ -c "/dev/video0" ]; then
        print_status "Camera devices found: $(ls /dev/video* | wc -l) devices"
    else
        print_error "No camera devices found"
    fi
}

# Function to start the system
start_system() {
    print_header "Starting Raspberry Pi Pose Estimation System"
    echo ""

    # ALWAYS stop any existing processes first - this ensures clean startup
    if [ "$AUTO_STOP" = true ]; then
        print_header "Cleaning up any existing processes..."
        stop_processes
        echo ""
    else
        print_warning "Skipping automatic cleanup (--no-stop was used)"
        echo ""
    fi
    
    # Start backend
    print_header "Starting Backend (Pose Estimation)..."
    print_status "Camera input: $CAMERA_INPUT"
    print_status "Command: $BACKEND_SCRIPT --input $CAMERA_INPUT"
    
    # Start backend in background
    nohup $BACKEND_SCRIPT --input $CAMERA_INPUT > backend.log 2>&1 &
    BACKEND_PID=$!
    
    print_status "Backend started with PID: $BACKEND_PID"
    print_status "Backend logs: $SCRIPT_DIR/backend.log"
    
    # Wait for shared memory to be created
    print_status "Waiting for shared memory to be created..."
    for i in {1..30}; do
        if [ -f "/dev/shm/pose_shm" ]; then
            print_status "Shared memory created successfully"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "Timeout waiting for shared memory. Check backend.log for errors."
            exit 1
        fi
        sleep 1
        echo -n "."
    done
    echo ""
    
    # Start frontend
    print_header "Starting Frontend (Web Server)..."
    print_status "Command: $FRONTEND_SCRIPT"
    
    # Start frontend in background
    nohup $FRONTEND_SCRIPT > frontend.log 2>&1 &
    FRONTEND_PID=$!
    
    print_status "Frontend started with PID: $FRONTEND_PID"
    print_status "Frontend logs: $SCRIPT_DIR/frontend.log"
    
    # Wait a moment for frontend to start
    sleep 3

    # Open browser automatically
    if [ "$OPEN_BROWSER" = true ]; then
        print_header "Opening Web Browser..."
        if command -v xdg-open > /dev/null; then
            xdg-open http://localhost:5000 > /dev/null 2>&1 &
            print_status "Browser opened automatically"
        elif command -v chromium-browser > /dev/null; then
            chromium-browser http://localhost:5000 > /dev/null 2>&1 &
            print_status "Chromium browser opened automatically"
        elif command -v firefox > /dev/null; then
            firefox http://localhost:5000 > /dev/null 2>&1 &
            print_status "Firefox browser opened automatically"
        else
            print_warning "No browser found for automatic opening"
            print_status "Please open http://localhost:5000 manually"
        fi
        sleep 1
    fi

    # Final status
    echo ""
    print_header "Startup Complete!"
    print_status "Backend PID: $BACKEND_PID"
    print_status "Frontend PID: $FRONTEND_PID"
    print_status "Web interface: http://localhost:5000"
    print_status "Logs: backend.log, frontend.log"
    echo ""
    print_warning "Use './start.sh --stop' to stop all processes"
    print_warning "Use './start.sh --status' to check system status"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --camera-input)
            CAMERA_INPUT="$2"
            shift 2
            ;;
        --performance)
            ./switch_video_sink.sh performance
            print_status "Switched to performance mode (fakesink)"
            shift
            ;;
        --display)
            ./switch_video_sink.sh display
            print_status "Switched to display mode (autovideosink)"
            shift
            ;;
        --no-stop)
            AUTO_STOP=false
            print_warning "Automatic process cleanup disabled"
            shift
            ;;
        --no-browser)
            OPEN_BROWSER=false
            print_warning "Automatic browser opening disabled"
            shift
            ;;
        --stop)
            stop_processes
            exit 0
            ;;
        --status)
            show_status
            exit 0
            ;;
        --help|-h)
            echo "Raspberry Pi Pose Estimation & Hand Ninja Game Startup Script"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --camera-input INPUT    Camera input (rpi, usb, /dev/videoX) [default: rpi]"
            echo "  --performance          Use fakesink (performance mode)"
            echo "  --display              Use autovideosink (display mode)"
            echo "  --no-stop              Skip automatic cleanup of existing processes"
            echo "  --no-browser           Skip automatic browser opening"
            echo "  --stop                 Stop all running processes"
            echo "  --status               Show status of running processes"
            echo "  --help                 Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                           # Start with default settings (rpi camera)"
            echo "  $0 --camera-input usb        # Start with USB camera"
            echo "  $0 --performance             # Start in performance mode"
            echo "  $0 --stop                    # Stop all processes"
            echo "  $0 --status                  # Check system status"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use '$0 --help' for usage information"
            exit 1
            ;;
    esac
done

# Main execution
start_system
