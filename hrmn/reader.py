#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time, datetime as dt
from shared_memory import open_pose_shm

shm, poses = open_pose_shm()  # shape=(P,17,3)

def ts():
    return dt.datetime.now().strftime("%y.%m.%d; %H:%M:%S")

print(f"[{ts()}] Reader gestartet – lese Pose-Daten aus /pose_shm.")
frame_cnt=0

try:
    while True:
        snap = poses.copy()
        frame_cnt+=1
        # minimal: prüfen, ob person 0 existiert
        exist_mask = (snap[:,0,2]>0)
        n = int(exist_mask.sum())

        msg = f"Frame {frame_cnt:05d} → Persons={n}"
        if n:
            # z.B. linke Schulter Person 0 = index 5
            ls_x, ls_y, ls_c = snap[0,5]
            msg+=f"  LS=({ls_x:.3f},{ls_y:.3f},c={ls_c:.2f})"
        print(f"[{ts()}] {msg}", flush=True)
        time.sleep(0.2)
except KeyboardInterrupt:
    print(f"[{ts()}] CTRL-C, bye.")
finally:
    shm.close()
