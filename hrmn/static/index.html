<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<title>Live Pose</title>
<script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
<style>
  body { margin:0; background:#111; display:flex; justify-content:center; align-items:center; height:100vh; flex-direction:column; }
  canvas { background:#222; margin-bottom: 20px; }
  #status { color: white; font-family: Arial, sans-serif; margin-bottom: 10px; }
  #debug { color: #999; font-family: monospace; font-size: 12px; margin-top: 10px; max-width: 640px; overflow: auto; }
</style>
</head>
<body>
<div id="status">Connecting...</div>
<canvas id="c" width="640" height="480"></canvas>
<div id="debug"></div>

<script>
const canvas = document.getElementById("c");
const ctx = canvas.getContext("2d");
const W = canvas.width, H = canvas.height;
const statusEl = document.getElementById("status");
const debugEl = document.getElementById("debug");

// Log to both console and debug element
function log(msg) {
  console.log(msg);
  debugEl.textContent += msg + "\n";
  if (debugEl.textContent.split("\n").length > 10) {
    const lines = debugEl.textContent.split("\n");
    debugEl.textContent = lines.slice(lines.length - 10).join("\n");
  }
}

const LINKS = [
  [5,7],[7,9], [6,8],[8,10], [11,13],[13,15], [12,14],[14,16],
  [5,6],[11,12],[5,11],[6,12]
];

// Create Socket.IO connection - using the approach from test_socket_simple.py
log("Creating Socket.IO connection...");
const socket = io("/pose");

socket.on("connect", () => {
  log("Socket connected with ID: " + socket.id);
  statusEl.textContent = "Connected";
  statusEl.style.color = "green";
});

socket.on("connect_error", (error) => {
  log("Connection error: " + error);
  statusEl.textContent = "Connection Error";
  statusEl.style.color = "red";
});

socket.on("disconnect", (reason) => {
  log("Socket disconnected, reason: " + reason);
  statusEl.textContent = "Disconnected";
  statusEl.style.color = "red";
});

socket.on("pose", data => {
  log("Received pose data: " + data.length + " persons");
  
  ctx.clearRect(0, 0, W, H);
  if (data.length === 0) {
    ctx.fillStyle = "#f00";
    ctx.font = "20px Arial";
    ctx.fillText("No pose data received", 20, 30);
    return;
  }
  
  data.forEach(person => {
    const k = person.kpts; // 17 keypoints: [x,y,conf]
    
    // Draw skeleton lines
    ctx.strokeStyle = "#55d";
    ctx.lineWidth = 2;
    LINKS.forEach(([a,b]) => {
      if (k[a][2] > 0 && k[b][2] > 0) {
        ctx.beginPath();
        ctx.moveTo(k[a][0] * W, k[a][1] * H);
        ctx.lineTo(k[b][0] * W, k[b][1] * H);
        ctx.stroke();
      }
    });
    
    // Draw keypoints
    ctx.fillStyle = "#0f0";
    k.forEach(([x, y, c]) => {
      if (c <= 0) return;
      ctx.beginPath();
      ctx.arc(x * W, y * H, 4, 0, 2 * Math.PI);
      ctx.fill();
    });
    
    // Draw person ID
    ctx.fillStyle = "#fff";
    ctx.font = "16px Arial";
    ctx.fillText("ID: " + person.id, k[0][0] * W, k[0][1] * H - 10);
  });
});

// Additional debugging
socket.io.on("error", (error) => {
  log("Transport error: " + error);
});

socket.io.on("reconnect_attempt", (attempt) => {
  log("Reconnection attempt: " + attempt);
});
</script>
</body>
</html>
