<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>n <PERSON>l</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            color: white;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #gameCanvas {
            display: block;
            margin: 0 auto;
            border: 2px solid #333;
        }
        
        #ui {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
        }
        
        #status {
            color: red;
            font-weight: bold;
        }
        
        .connected { color: green !important; }
        
        #score {
            font-size: 24px;
            margin: 10px 0;
        }
        
        #instructions {
            position: absolute;
            top: 10px;
            right: 10px;
            max-width: 300px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div id="ui">
        <div id="status">Connecting...</div>
        <div id="score">Score: 0</div>
        <div id="fps">FPS: 0</div>
    </div>
    
    <div id="instructions">
        <h3>🎮 Mein Neues Spiel</h3>
        <p>Bewege deine Hände, um zu spielen!</p>
        <p>Linke Hand: Rot</p>
        <p>Rechte Hand: Blau</p>
    </div>
    
    <canvas id="gameCanvas"></canvas>

    <script>
        // Canvas Setup
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // UI Elements
        const statusEl = document.getElementById('status');
        const scoreEl = document.getElementById('score');
        const fpsEl = document.getElementById('fps');
        
        // Game Variables
        let score = 0;
        let gameActive = true;
        
        // Screen dimensions
        let W, H;
        
        function resizeCanvas() {
            W = window.innerWidth;
            H = window.innerHeight;
            canvas.width = W;
            canvas.height = H;
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        // Keypoint indices (same as server)
        const NOSE = 0;
        const LEFT_SHOULDER = 1;
        const LEFT_ELBOW = 2;
        const LEFT_WRIST = 3;
        const RIGHT_SHOULDER = 4;
        const RIGHT_ELBOW = 5;
        const RIGHT_WRIST = 6;
        
        // Player data
        let players = {};
        
        // FPS tracking
        let frameCount = 0;
        let lastFpsTime = Date.now();
        let currentFps = 0;
        
        // Socket.IO connection
        console.log("Creating Socket.IO connection...");
        const socket = io("/pose", {
            transports: ["websocket"],
            upgrade: false,
            reconnectionDelay: 100,
            timeout: 5000,
            forceNew: true
        });
        
        socket.on("connect", () => {
            console.log("Socket connected with ID: " + socket.id);
            statusEl.textContent = "Connected";
            statusEl.className = "connected";
        });
        
        socket.on("connect_error", (error) => {
            console.log("Connection error: " + error);
            statusEl.textContent = "Connection Error";
            statusEl.className = "";
        });
        
        socket.on("disconnect", () => {
            console.log("Socket disconnected");
            statusEl.textContent = "Disconnected";
            statusEl.className = "";
        });
        
        // Process pose data
        socket.on("pose", data => {
            // Update FPS counter
            frameCount++;
            const now = Date.now();
            if (now - lastFpsTime >= 1000) {
                currentFps = frameCount;
                frameCount = 0;
                lastFpsTime = now;
                fpsEl.textContent = `FPS: ${currentFps}`;
            }
            
            if (!gameActive) return;
            
            // Clear previous player data
            players = {};
            
            // Process each detected person
            data.forEach((person, index) => {
                const kpts = person.kpts;
                
                if (kpts && kpts.length >= 7) {
                    players[index] = {
                        nose: { x: kpts[NOSE][0] * W, y: kpts[NOSE][1] * H, conf: kpts[NOSE][2] },
                        leftShoulder: { x: kpts[LEFT_SHOULDER][0] * W, y: kpts[LEFT_SHOULDER][1] * H, conf: kpts[LEFT_SHOULDER][2] },
                        leftElbow: { x: kpts[LEFT_ELBOW][0] * W, y: kpts[LEFT_ELBOW][1] * H, conf: kpts[LEFT_ELBOW][2] },
                        leftWrist: { x: kpts[LEFT_WRIST][0] * W, y: kpts[LEFT_WRIST][1] * H, conf: kpts[LEFT_WRIST][2] },
                        rightShoulder: { x: kpts[RIGHT_SHOULDER][0] * W, y: kpts[RIGHT_SHOULDER][1] * H, conf: kpts[RIGHT_SHOULDER][2] },
                        rightElbow: { x: kpts[RIGHT_ELBOW][0] * W, y: kpts[RIGHT_ELBOW][1] * H, conf: kpts[RIGHT_ELBOW][2] },
                        rightWrist: { x: kpts[RIGHT_WRIST][0] * W, y: kpts[RIGHT_WRIST][1] * H, conf: kpts[RIGHT_WRIST][2] }
                    };
                    
                    // Hier kannst du deine Spiel-Logik einfügen
                    processGameLogic(players[index], index);
                }
            });
        });
        
        // Game Logic Function - HIER DEINE SPIEL-LOGIK EINFÜGEN
        function processGameLogic(player, playerIndex) {
            // Beispiel: Punkte für Handbewegungen
            if (player.leftWrist.conf > 0.5 || player.rightWrist.conf > 0.5) {
                // Hier kannst du deine Spiel-Mechaniken implementieren
                // z.B. Kollisionserkennung, Objektinteraktion, etc.
            }
        }
        
        // Game Update Loop
        function update() {
            requestAnimationFrame(update);
            
            // Clear canvas
            ctx.clearRect(0, 0, W, H);
            
            // Draw background
            ctx.fillStyle = "#111";
            ctx.fillRect(0, 0, W, H);
            
            // Draw players
            Object.keys(players).forEach(playerId => {
                const player = players[playerId];
                drawPlayer(player, playerId);
            });
            
            // Draw UI
            drawUI();
        }
        
        function drawPlayer(player, playerId) {
            const color = playerId == 0 ? "#ff4444" : "#4444ff";
            
            // Draw skeleton
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            
            // Draw arms
            if (player.leftShoulder.conf > 0.5 && player.leftElbow.conf > 0.5) {
                ctx.beginPath();
                ctx.moveTo(player.leftShoulder.x, player.leftShoulder.y);
                ctx.lineTo(player.leftElbow.x, player.leftElbow.y);
                ctx.stroke();
            }
            
            if (player.leftElbow.conf > 0.5 && player.leftWrist.conf > 0.5) {
                ctx.beginPath();
                ctx.moveTo(player.leftElbow.x, player.leftElbow.y);
                ctx.lineTo(player.leftWrist.x, player.leftWrist.y);
                ctx.stroke();
            }
            
            if (player.rightShoulder.conf > 0.5 && player.rightElbow.conf > 0.5) {
                ctx.beginPath();
                ctx.moveTo(player.rightShoulder.x, player.rightShoulder.y);
                ctx.lineTo(player.rightElbow.x, player.rightElbow.y);
                ctx.stroke();
            }
            
            if (player.rightElbow.conf > 0.5 && player.rightWrist.conf > 0.5) {
                ctx.beginPath();
                ctx.moveTo(player.rightElbow.x, player.rightElbow.y);
                ctx.lineTo(player.rightWrist.x, player.rightWrist.y);
                ctx.stroke();
            }
            
            // Draw hands
            ctx.fillStyle = color;
            if (player.leftWrist.conf > 0.5) {
                ctx.beginPath();
                ctx.arc(player.leftWrist.x, player.leftWrist.y, 15, 0, Math.PI * 2);
                ctx.fill();
            }
            
            if (player.rightWrist.conf > 0.5) {
                ctx.beginPath();
                ctx.arc(player.rightWrist.x, player.rightWrist.y, 15, 0, Math.PI * 2);
                ctx.fill();
            }
        }
        
        function drawUI() {
            // Draw score
            ctx.fillStyle = "#fff";
            ctx.font = "24px Arial";
            ctx.fillText(`Score: ${score}`, 20, H - 40);
        }
        
        // Start the game
        update();
        
        console.log("🎮 Mein Neues Spiel gestartet!");
    </script>
</body>
</html>
