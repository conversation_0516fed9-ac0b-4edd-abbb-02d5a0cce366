#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wartet auf /pose_shm, schickt Pose-Daten (~30 FPS) via Socket.IO an /pose.
Serviert index.html aus dem Ordner “static/” (gle<PERSON><PERSON>).
"""

# ── eventlet first ──────────────────────────────────────────────────────────────
import eventlet
eventlet.monkey_patch()              # patches std-lib for eventlet greenlets

# ── standard libs / 3rd-party ──────────────────────────────────────────────────
import time, threading, pathlib, sys
import numpy as np
from flask import Flask, send_from_directory
from flask_socketio import SocketIO

# ── project local ───────────────────────────────────────────────────────────────
from shared_memory import open_pose_shm, POSE_SHM_NAME

# ── paths ───────────────────────────────────────────────────────────────────────
BASE_DIR   = pathlib.Path(__file__).parent
STATIC_DIR = BASE_DIR / "static"

# ── Flask + Socket.IO ──────────────────────────────────────────────────────────
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret!'  # Add a secret key
socketio = SocketIO(
    app,
    async_mode="eventlet",
    cors_allowed_origins="*",
    logger=True,
    engineio_logger=True
)

# ── wait for the shared-memory segment from pose_estimation.py ────────────────
def wait_for_shm():
    while True:
        try:
            return open_pose_shm()
        except FileNotFoundError:
            print("[WAIT] /pose_shm noch nicht da – 1 s sleep …", file=sys.stderr)
            time.sleep(1)

shm, poses = wait_for_shm()
MAX_PERSONS, NUM_KPTS = poses.shape[:2]
print(f"[INFO] Connected to shared memory '{POSE_SHM_NAME}' with shape {poses.shape}")

# ── Socket.IO events ─────────────────────────────────────────────────────────
@socketio.on('connect', namespace='/pose')
def handle_connect():
    print('[Socket.IO] Client connected to /pose namespace')

@socketio.on('disconnect', namespace='/pose')
def handle_disconnect():
    print('[Socket.IO] Client disconnected from /pose namespace')

# ── broadcaster thread ─────────────────────────────────────────────────────────
def broadcast_loop():
    """Read from shared memory and broadcast via Socket.IO"""
    frame_count = 0
    while True:
        # Take a snapshot of the current state
        snap = poses.copy()            # (P,17,3)
        
        # Check for valid persons (nose confidence > 0)
        valid_mask = (snap[:, 0, 2] > 0)
        valid_count = int(valid_mask.sum())
        
        # Prepare payload with valid persons
        payload = [
            {"id": idx, "kpts": person.tolist()}
            for idx, person in enumerate(snap)
            if person[0, 2] > 0
        ]
        
        # Emit via Socket.IO
        socketio.emit("pose", payload, namespace="/pose")
        
        frame_count += 1
        if frame_count % 30 == 0:  # Log every 30 frames to reduce spam
            print(f"[Socket] Frame {frame_count}: Emitted {valid_count} persons")
            if valid_count > 0:
                first_valid_idx = np.argmax(valid_mask)
                print(f"[Socket] Person {first_valid_idx} nose: {snap[first_valid_idx, 0]}")
        
        eventlet.sleep(0.033)          # green-sleep ~30 FPS

# Start thread
threading.Thread(target=broadcast_loop, daemon=True).start()

# ── HTTP routes ────────────────────────────────────────────────────────────────
@app.route("/")
def root():
    return send_from_directory(STATIC_DIR, "index.html")

@app.route("/<path:filename>")
def static_file(filename):
    return send_from_directory(STATIC_DIR, filename)

# ── main ────────────────────────────────────────────────────────────────────────
if __name__ == "__main__":
    print(f"[INFO] Starte Pose-Socket auf http://0.0.0.0:5000  (SHM='{POSE_SHM_NAME}')")
    try:
        socketio.run(app, host="0.0.0.0", port=5000, debug=True)
    except Exception as e:
        print(f"[ERROR] Server error: {e}")
    finally:
        shm.close()
