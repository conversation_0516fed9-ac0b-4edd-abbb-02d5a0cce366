#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Latency test using the same infrastructure as the ninja game.
Uses Socket.IO to read pose data and display latency test interface.
"""

import eventlet
eventlet.monkey_patch()

from flask import Flask, send_from_directory
from flask_socketio import Socket<PERSON>
import numpy as np
import threading
import pathlib
import sys

# Import shared memory functions
from shared_memory import open_pose_shm, POSE_SHM_NAME

# Paths
BASE_DIR = pathlib.Path(__file__).parent
STATIC_DIR = BASE_DIR / "static"

# Create Flask app and Socket.IO - optimized
app = Flask(__name__)
app.config['SECRET_KEY'] = 'secret!'
socketio = SocketIO(
    app,
    async_mode="eventlet",
    cors_allowed_origins="*",
    logger=False,
    engineio_logger=False
)

# Try to connect to shared memory
try:
    print(f"[Server] Connecting to shared memory '{POSE_SHM_NAME}'...")
    shm, poses = open_pose_shm()
    MAX_PERSONS, NUM_KPTS = poses.shape[:2]
    print(f"[Server] Connected to shared memory with shape {poses.shape}")
except FileNotFoundError:
    print(f"[Server] ERROR: Shared memory '{POSE_SHM_NAME}' not found!")
    print(f"[Server] Make sure the writer process is running first.")
    sys.exit(1)

# Socket.IO event handlers
@socketio.on('connect', namespace='/pose')
def handle_connect():
    print('[Socket.IO] Client connected to /pose namespace')

@socketio.on('disconnect', namespace='/pose')
def handle_disconnect():
    print('[Socket.IO] Client disconnected from /pose namespace')

# Broadcaster thread - same as ninja game
def broadcast_loop():
    """Read from shared memory and broadcast via Socket.IO"""
    frame_count = 0
    # Pre-allocate buffer for better performance
    buf = np.empty((MAX_PERSONS, NUM_KPTS, 3), dtype=np.float32)
    
    # Keypoint indices we care about
    keypoint_indices = [0, 5, 7, 9, 6, 8, 10]  # nose, left/right shoulder, elbow, wrist
    
    while True:
        try:
            # Copy only the data we need instead of the entire array
            np.copyto(buf, poses)
            
            # Check for valid persons (nose confidence > 0)
            valid_mask = (buf[:, 0, 2] > 0)
            valid_count = int(valid_mask.sum())
            
            if valid_count > 0:
                # Extract only the keypoints we need for each valid person
                valid_persons = []
                for idx in range(MAX_PERSONS):
                    if buf[idx, 0, 2] > 0:  # If nose confidence > 0
                        # Extract only the keypoints we need
                        person_data = {
                            "id": idx,
                            "kpts": [buf[idx, kp_idx].tolist() for kp_idx in keypoint_indices]
                        }
                        valid_persons.append(person_data)
                
                # Emit via Socket.IO
                socketio.emit("pose", valid_persons, namespace="/pose")
            
            frame_count += 1
            if frame_count % 100 == 0:  # Log less frequently
                print(f"[Socket] Frame {frame_count}: Emitted {valid_count} persons")
            
            eventlet.sleep(0.05)  # 20 FPS for better performance
        except Exception as e:
            print(f"[Socket] Error in broadcast_loop: {e}")
            eventlet.sleep(1.0)  # Sleep longer on error

# Start thread
threading.Thread(target=broadcast_loop, daemon=True).start()

# HTTP routes
@app.route("/")
def root():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Hand Latency Test</title>
        <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
        <link rel="icon" href="data:,">
        <style>
            body { 
                margin: 0; 
                background: #111; 
                display: flex; 
                justify-content: center; 
                align-items: center; 
                height: 100vh; 
                flex-direction: column;
                font-family: Arial, sans-serif;
            }
            canvas { 
                background: #222; 
                margin-bottom: 20px; 
                border: 2px solid #444;
            }
            #status { 
                color: white; 
                font-family: Arial, sans-serif; 
                margin-bottom: 10px; 
            }
            .stats {
                color: white;
                font-size: 18px;
                margin-bottom: 20px;
                display: flex;
                gap: 30px;
            }
            .stat-item {
                text-align: center;
            }
            .stat-value {
                font-size: 2em;
                font-weight: bold;
                color: #4CAF50;
            }
            .stat-label {
                font-size: 0.8em;
                color: #999;
            }
            .hands-info {
                color: white;
                margin-bottom: 20px;
                display: flex;
                gap: 40px;
                font-family: monospace;
            }
            .hand-info {
                text-align: center;
                padding: 10px;
                border-radius: 5px;
            }
            .left-hand-info { background: rgba(255, 85, 85, 0.2); }
            .right-hand-info { background: rgba(85, 255, 85, 0.2); }
            .latency-test {
                background: #333;
                padding: 20px;
                border-radius: 10px;
                margin-top: 20px;
                text-align: center;
                color: white;
            }
            .test-button {
                background: #4CAF50;
                color: white;
                border: none;
                padding: 15px 30px;
                font-size: 1.2em;
                border-radius: 5px;
                cursor: pointer;
                margin: 10px;
            }
            .test-button:hover {
                background: #45a049;
            }
            .latency-result {
                font-size: 1.5em;
                margin-top: 20px;
                padding: 10px;
                border-radius: 5px;
            }
            .good-latency { background: #4CAF50; }
            .medium-latency { background: #ff9800; }
            .bad-latency { background: #f44336; }
            #debug { 
                color: #999; 
                font-family: monospace; 
                font-size: 12px; 
                margin-top: 10px; 
                max-width: 640px; 
                overflow: auto; 
                height: 100px; 
            }
        </style>
    </head>
    <body>
        <div id="status">Connecting...</div>
        <h2 style="color: white;">Hand Latency Test</h2>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="fps">0</div>
                <div class="stat-label">FPS</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="persons">0</div>
                <div class="stat-label">Persons</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="latency">-</div>
                <div class="stat-label">Latency (ms)</div>
            </div>
        </div>
        
        <div class="hands-info">
            <div class="hand-info left-hand-info">
                <div><strong>Left Hand</strong></div>
                <div id="left-hand-data">No data</div>
            </div>
            <div class="hand-info right-hand-info">
                <div><strong>Right Hand</strong></div>
                <div id="right-hand-data">No data</div>
            </div>
        </div>
        
        <canvas id="gameCanvas" width="320" height="240" style="width:640px; height:480px;"></canvas>
        
        <div class="latency-test">
            <h3>Latency Test</h3>
            <p>Click the button and immediately move your hand to test response time:</p>
            <button class="test-button" onclick="startLatencyTest()">Start Latency Test</button>
            <div id="latency-result" class="latency-result" style="display: none;"></div>
        </div>
        
        <div id="debug"></div>
        
        <script>
            // Game canvas setup - OPTIMIZED
            const canvas = document.getElementById("gameCanvas");
            const ctx = canvas.getContext("2d");
            const W = canvas.width, H = canvas.height;
            // Disable image smoothing for better performance
            ctx.imageSmoothingEnabled = false;
            const statusEl = document.getElementById("status");
            const debugEl = document.getElementById("debug");
            
            // Keypoint indices for both arms (same as ninja game)
            const NOSE = 0;
            const LEFT_SHOULDER = 1;
            const LEFT_ELBOW = 2;
            const LEFT_WRIST = 3;
            const RIGHT_SHOULDER = 4;
            const RIGHT_ELBOW = 5;
            const RIGHT_WRIST = 6;
            
            // Hand positions
            let leftHandPosition = null;
            let rightHandPosition = null;
            
            // Latency test variables
            let testStartTime = 0;
            let testActive = false;
            let lastHandPosition = null;
            let frameCount = 0;
            let lastFpsTime = Date.now();
            let currentFps = 0;
            let lastUpdateTime = 0;
            
            // Log to both console and debug element
            function log(msg) {
                console.log(msg);
                debugEl.textContent += msg + "\\n";
                if (debugEl.textContent.split("\\n").length > 10) {
                    const lines = debugEl.textContent.split("\\n");
                    debugEl.textContent = lines.slice(lines.length - 10).join("\\n");
                }
                debugEl.scrollTop = debugEl.scrollHeight;
            }
            
            // Create Socket.IO connection - same as ninja game
            log("Creating Socket.IO connection...");
            const socket = io("/pose", {
                transports: ["websocket"],
                upgrade: false,
                reconnectionDelay: 100,
                timeout: 5000,
                forceNew: true,
                perMessageDeflate: false
            });
            
            socket.on("connect", () => {
                log("Socket connected with ID: " + socket.id);
                statusEl.textContent = "Connected";
                statusEl.style.color = "green";
            });
            
            socket.on("connect_error", (error) => {
                log("Connection error: " + error);
                statusEl.textContent = "Connection Error";
                statusEl.style.color = "red";
            });
            
            socket.on("disconnect", (reason) => {
                log("Socket disconnected, reason: " + reason);
                statusEl.textContent = "Disconnected";
                statusEl.style.color = "red";
            });
            
            // Process pose data - same as ninja game
            socket.on("pose", data => {
                frameCount++;
                const now = Date.now();
                
                // Calculate FPS
                if (now - lastFpsTime >= 1000) {
                    currentFps = frameCount;
                    frameCount = 0;
                    lastFpsTime = now;
                    document.getElementById('fps').textContent = currentFps;
                }
                
                // Calculate latency
                if (lastUpdateTime > 0) {
                    const latency = now - lastUpdateTime;
                    document.getElementById('latency').textContent = latency + 'ms';
                }
                lastUpdateTime = now;
                
                document.getElementById('persons').textContent = data.length;
                
                if (data.length > 0) {
                    // Get the first person's keypoints
                    const person = data[0];
                    const kpts = person.kpts;
                    
                    // Make sure we have all the keypoints we need
                    if (!kpts || kpts.length < 7) {
                        console.error("Incomplete keypoints data received:", kpts);
                        return;
                    }
                    
                    // Process left hand
                    if (kpts[LEFT_WRIST] && kpts[LEFT_WRIST][2] > 0) {
                        leftHandPosition = {
                            x: kpts[LEFT_WRIST][0] * W,
                            y: kpts[LEFT_WRIST][1] * H,
                            confidence: kpts[LEFT_WRIST][2]
                        };
                        
                        document.getElementById('left-hand-data').innerHTML = 
                            `X: ${kpts[LEFT_WRIST][0].toFixed(3)}<br>` +
                            `Y: ${kpts[LEFT_WRIST][1].toFixed(3)}<br>` +
                            `Conf: ${kpts[LEFT_WRIST][2].toFixed(3)}`;
                    } else {
                        leftHandPosition = null;
                        document.getElementById('left-hand-data').innerHTML = 'Not visible';
                    }
                    
                    // Process right hand
                    if (kpts[RIGHT_WRIST] && kpts[RIGHT_WRIST][2] > 0) {
                        rightHandPosition = {
                            x: kpts[RIGHT_WRIST][0] * W,
                            y: kpts[RIGHT_WRIST][1] * H,
                            confidence: kpts[RIGHT_WRIST][2]
                        };
                        
                        document.getElementById('right-hand-data').innerHTML = 
                            `X: ${kpts[RIGHT_WRIST][0].toFixed(3)}<br>` +
                            `Y: ${kpts[RIGHT_WRIST][1].toFixed(3)}<br>` +
                            `Conf: ${kpts[RIGHT_WRIST][2].toFixed(3)}`;
                        
                        // Check for latency test
                        if (testActive) {
                            checkLatencyTest();
                        }
                    } else {
                        rightHandPosition = null;
                        document.getElementById('right-hand-data').innerHTML = 'Not visible';
                    }
                } else {
                    leftHandPosition = null;
                    rightHandPosition = null;
                    document.getElementById('left-hand-data').innerHTML = 'No person';
                    document.getElementById('right-hand-data').innerHTML = 'No person';
                }
            });
            
            function startLatencyTest() {
                testStartTime = Date.now();
                testActive = true;
                lastHandPosition = rightHandPosition ? {x: rightHandPosition.x, y: rightHandPosition.y} : null;
                
                document.getElementById('latency-result').style.display = 'none';
                
                // Show instruction
                const resultEl = document.getElementById('latency-result');
                resultEl.innerHTML = 'Move your RIGHT hand NOW!';
                resultEl.className = 'latency-result';
                resultEl.style.display = 'block';
                resultEl.style.background = '#2196F3';
            }
            
            function checkLatencyTest() {
                if (!testActive || !rightHandPosition || !lastHandPosition) return;
                
                const dx = rightHandPosition.x - lastHandPosition.x;
                const dy = rightHandPosition.y - lastHandPosition.y;
                const distance = Math.sqrt(dx*dx + dy*dy);
                
                // If hand moved significantly (threshold: 20 pixels)
                if (distance > 20) {
                    const latency = Date.now() - testStartTime;
                    testActive = false;
                    
                    const resultEl = document.getElementById('latency-result');
                    resultEl.innerHTML = `Latency: ${latency}ms`;
                    
                    if (latency < 100) {
                        resultEl.className = 'latency-result good-latency';
                    } else if (latency < 200) {
                        resultEl.className = 'latency-result medium-latency';
                    } else {
                        resultEl.className = 'latency-result bad-latency';
                    }
                }
            }
            
            // Update function - simplified for latency test
            function update() {
                requestAnimationFrame(update);
                
                // Clear canvas
                ctx.clearRect(0, 0, W, H);
                
                // Draw background
                ctx.fillStyle = "#222";
                ctx.fillRect(0, 0, W, H);
                
                // Draw left hand
                if (leftHandPosition) {
                    ctx.fillStyle = "#f55";  // Red for left hand
                    ctx.beginPath();
                    ctx.arc(leftHandPosition.x, leftHandPosition.y, 15, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // Label
                    ctx.fillStyle = "#fff";
                    ctx.font = "16px Arial";
                    ctx.textAlign = "center";
                    ctx.fillText("L", leftHandPosition.x, leftHandPosition.y + 5);
                }
                
                // Draw right hand
                if (rightHandPosition) {
                    ctx.fillStyle = "#5f5";  // Green for right hand
                    ctx.beginPath();
                    ctx.arc(rightHandPosition.x, rightHandPosition.y, 15, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // Label
                    ctx.fillStyle = "#fff";
                    ctx.font = "16px Arial";
                    ctx.textAlign = "center";
                    ctx.fillText("R", rightHandPosition.x, rightHandPosition.y + 5);
                }
                
                // Draw instructions
                ctx.fillStyle = "#fff";
                ctx.font = "16px Arial";
                ctx.textAlign = "center";
                ctx.fillText("Move your hands to see tracking", W/2, 30);
            }
            
            // Start the update loop
            update();
        </script>
    </body>
    </html>
    """

@app.route("/<path:filename>")
def static_file(filename):
    # First try to serve from static directory
    try:
        return send_from_directory(STATIC_DIR, filename)
    except:
        # If not found, try to serve from the image directory
        image_dir = pathlib.Path("/home/<USER>/Documents/taha_ai/hrmn")
        return send_from_directory(image_dir, filename)

if __name__ == "__main__":
    print(f"[Server] Starting Hand Latency Test server on http://0.0.0.0:5002")
    print(f"[Server] Using same infrastructure as ninja game")
    try:
        # Turn off debug mode for better performance
        socketio.run(app, host="0.0.0.0", port=5002, debug=False)
    except Exception as e:
        print(f"[Server] Error starting server: {e}")
    finally:
        shm.close()
