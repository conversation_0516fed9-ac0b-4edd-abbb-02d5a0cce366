# Video Sink Configuration System

This system allows you to easily switch between display and performance modes for all Hailo pose estimation scripts.

## Quick Start

### Check current mode:
```bash
./switch_video_sink.sh
```

### Switch to performance mode (no video display):
```bash
./switch_video_sink.sh performance
```

### Switch to display mode (show video output):
```bash
./switch_video_sink.sh display
```

## Available Modes

### Display Mode (`autovideosink`)
- **Shows video output** with pose estimation overlays
- **Good for debugging** and visual feedback
- **Higher CPU usage** due to video rendering
- **Default mode**

### Performance Mode (`fakesink`)
- **No video display** - data processing only
- **Better performance** and lower CPU usage
- **Ideal for production** and headless operation
- **Recommended for Raspberry Pi** when maximum performance is needed

## How It Works

1. **Global Configuration**: The `video_sink_config.conf` file controls the behavior
2. **Automatic Detection**: All scripts automatically read this configuration
3. **Smart Patching**: Monkey-patches are only applied when needed
4. **Environment Variables**: GStreamer environment variables are set accordingly

## Files Involved

- `video_sink_config.conf` - Configuration file
- `switch_video_sink.sh` - Easy switching script
- `run_pose_estimation.sh` - Updated to use configuration
- `run_hand_pose_estimation.sh` - Updated to use configuration
- `pose_estimation.py` - Updated to read configuration
- `hand_pose_estimation.py` - Updated to read configuration

## Usage Examples

```bash
# Show help
./switch_video_sink.sh help

# Check current status
./switch_video_sink.sh status

# Switch modes (multiple aliases work)
./switch_video_sink.sh perf          # performance mode
./switch_video_sink.sh fakesink      # performance mode
./switch_video_sink.sh show          # display mode
./switch_video_sink.sh auto          # display mode

# After switching, restart your pose estimation:
./run_pose_estimation.sh --input rpi
```

## Important Notes

⚠️ **Always restart** pose estimation processes after changing the configuration!

✅ **Global Effect**: Changes affect ALL pose estimation scripts

🚀 **Performance**: Use performance mode for maximum FPS on Raspberry Pi

🔍 **Debugging**: Use display mode when you need visual feedback
