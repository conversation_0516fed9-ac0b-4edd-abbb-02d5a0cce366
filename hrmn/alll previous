#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gi, signal, sys, cv2, hailo
gi.require_version('Gst', '1.0')
from gi.repository import Gst, GLib

# ----------------------------------------------------------------------
# Hailo
# ----------------------------------------------------------------------
from hailo_apps_infra.hailo_rpi_common import (
    get_caps_from_pad,
    get_numpy_from_buffer,
    app_callback_class,
)
from hailo_apps_infra.pose_estimation_pipeline import GStreamerPoseEstimationApp

# ----------------------------------------------------------------------
# Shared Memory
# ----------------------------------------------------------------------
from shared_memory import init_pose_shm, POSE_SHM_NAME

MAX_PERSONS = 10
NUM_KPTS    = 17

pose_shm, pose_arr = init_pose_shm(MAX_PERSONS, NUM_KPTS)
# shape = (MAX_PERSONS,17,3)

# ----------------------------------------------------------------------
class UserAppCallback(app_callback_class):
    def __init__(self):
        super().__init__()

def app_callback(pad, info, user_data):
    buf = info.get_buffer()
    if not buf:
        return Gst.PadProbeReturn.OK

    # Frame → Array zurücksetzen
    pose_arr.fill(-1.0)

    user_data.increment()
    fmt, W, H = get_caps_from_pad(pad)
    frame = None
    if user_data.use_frame and None not in (fmt, W, H):
        frame = get_numpy_from_buffer(buf, fmt, W, H)

    import hailo
    roi = hailo.get_roi_from_buffer(buf)
    detections = roi.get_objects_typed(hailo.HAILO_DETECTION)

    person_idx = 0
    for det in detections:
        if det.get_label() != "person":
            continue
        if person_idx >= MAX_PERSONS:
            break

        lms = det.get_objects_typed(hailo.HAILO_LANDMARKS)
        if not lms:
            continue
        points = lms[0].get_points()
        bb = det.get_bbox()  # normalisierte BBox

        for kp_i, p in enumerate(points):
            x_norm = p.x() * bb.width() + bb.xmin()
            y_norm = p.y() * bb.height() + bb.ymin()
            conf   = getattr(p, "score", lambda:1.)()

            pose_arr[person_idx, kp_i] = (x_norm, y_norm, conf)

            # optionales Overlay
            if frame is not None:
                x_px = int(x_norm * W)
                y_px = int(y_norm * H)
                cv2.circle(frame, (x_px, y_px), 4, (0,255,0), -1)

        person_idx += 1

    # → Pipeline zurück
    if frame is not None:
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
        user_data.set_frame(frame)

    return Gst.PadProbeReturn.OK

# ----------------------------------------------------------------------
def cleanup(*_):
    print("\n[INFO] Kill pose_estimation.py – unlink shm …")
    pose_shm.close()
    pose_shm.unlink()
    sys.exit(0)

signal.signal(signal.SIGINT, cleanup)

# ----------------------------------------------------------------------
if __name__ == "__main__":
    print(f"[INFO] schreibe Pose-Daten in SHM='{POSE_SHM_NAME}'")
    user_data = UserAppCallback()
    app = GStreamerPoseEstimationApp(app_callback, user_data)
    try:
        app.run()
    finally:
        cleanup()


'''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''#

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time, datetime as dt
from shared_memory import open_pose_shm

shm, poses = open_pose_shm()  # shape=(P,17,3)

def ts():
    return dt.datetime.now().strftime("%y.%m.%d; %H:%M:%S")

print(f"[{ts()}] Reader gestartet – lese Pose-Daten aus /pose_shm.")
frame_cnt=0

try:
    while True:
        snap = poses.copy()
        frame_cnt+=1
        # minimal: prüfen, ob person 0 existiert
        exist_mask = (snap[:,0,2]>0)
        n = int(exist_mask.sum())

        msg = f"Frame {frame_cnt:05d} → Persons={n}"
        if n:
            # z.B. linke Schulter Person 0 = index 5
            ls_x, ls_y, ls_c = snap[0,5]
            msg+=f"  LS=({ls_x:.3f},{ls_y:.3f},c={ls_c:.2f})"
        print(f"[{ts()}] {msg}", flush=True)
        time.sleep(0.2)
except KeyboardInterrupt:
    print(f"[{ts()}] CTRL-C, bye.")
finally:
    shm.close()
''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''


#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wartet auf /pose_shm, schickt Pose-Daten ~30 FPS via Socket.IO an /pose.
Serviert index.html aus dem Ordner 'static/' (gleiches Verzeichnis).
"""

import time, threading, pathlib, sys
from flask import Flask, send_from_directory
from flask_socketio import SocketIO
from shared_memory import open_pose_shm, POSE_SHM_NAME

BASE_DIR = pathlib.Path(__file__).parent
STATIC_DIR = BASE_DIR / "static"

app = Flask(__name__)
socketio = SocketIO(app, cors_allowed_origins="*", async_mode="eventlet")

def wait_for_shm():
    while True:
        try:
            return open_pose_shm()
        except FileNotFoundError:
            print("[WAIT] /pose_shm noch nicht da – 1s sleep …", file=sys.stderr)
            time.sleep(1)

shm, poses = wait_for_shm()
MAX_PERSONS, NUM_KPTS = poses.shape[:2]

def broadcast_loop():
    while True:
        snap = poses.copy()
        payload = []
        for i, person in enumerate(snap):
            if person[0,2] > 0:   # Nase conf>0 => Person
                payload.append({"id": i, "kpts": person.tolist()})
        socketio.emit("pose", payload, namespace="/pose")
        print(f"[DEBUG] Sende payload = {payload[:1]}  (#={len(payload)})", flush=True)
        time.sleep(0.033)

threading.Thread(target=broadcast_loop, daemon=True).start()

@app.route("/")
def root():
    # Sendet /static/index.html
    return send_from_directory(STATIC_DIR, "index.html")

@app.route("/<path:filename>")
def static_file(filename):
    return send_from_directory(STATIC_DIR, filename)

if __name__ == "__main__":
    print(f"[INFO] Starte Pose-Socket auf http://0.0.0.0:5000  (SHM='{POSE_SHM_NAME}')")
    try:
        socketio.run(app, host="0.0.0.0", port=5000)
    finally:
        shm.close()

        
    #################################################################################################
        

        <!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<title>Live Pose</title>
<script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
<style>
  body { margin:0; background:#111; display:flex; justify-content:center; align-items:center; height:100vh }
  canvas { background:#222 }
</style>
</head>
<body>
<canvas id="c" width="640" height="480"></canvas>
<script>
const canvas=document.getElementById("c");
const ctx=canvas.getContext("2d");
const W=canvas.width, H=canvas.height;

const LINKS = [
  [5,7],[7,9], [6,8],[8,10], [11,13],[13,15], [12,14],[14,16],
  [5,6],[11,12],[5,11],[6,12]
];

const socket = io("/pose",{transports:["websocket"]});
socket.on("connect", ()=>console.log("WS connected"));
socket.on("disconnect",()=>console.log("WS disconnected"));

socket.on("pose", data=>{
  ctx.clearRect(0,0,W,H);
  data.forEach(person=>{
    const k=person.kpts; // 17 keypoints: [x,y,conf]
    ctx.strokeStyle="#55d"; ctx.lineWidth=2;
    LINKS.forEach(([a,b])=>{
      if(k[a][2]>0 && k[b][2]>0){
        ctx.beginPath();
        ctx.moveTo(k[a][0]*W,k[a][1]*H);
        ctx.lineTo(k[b][0]*W,k[b][1]*H);
        ctx.stroke();
      }
    });
    ctx.fillStyle="#0f0";
    k.forEach(([x,y,c])=>{
      if(c<=0) return;
      ctx.beginPath();
      ctx.arc(x*W,y*H,4,0,2*Math.PI);
      ctx.fill();
    });
  });
});
</script>
</body>
</html>
''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''

import numpy as np
from multiprocessing import shared_memory

POSE_SHM_NAME = "pose_shm"

def init_pose_shm(max_persons=10, num_kpts=17):
    """Erzeugt /pose_shm neu (vorheriges unlink)."""
    nbytes = max_persons * num_kpts * 3 * np.float32().nbytes
    try:
        old = shared_memory.SharedMemory(name=POSE_SHM_NAME)
        old.unlink(); old.close()
    except FileNotFoundError:
        pass

    shm = shared_memory.SharedMemory(create=True, size=nbytes, name=POSE_SHM_NAME)
    arr = np.ndarray((max_persons, num_kpts, 3), dtype=np.float32, buffer=shm.buf)
    arr.fill(-1.0)
    return shm, arr

def open_pose_shm(max_persons=10, num_kpts=17):
    """Öffnet /pose_shm (Fehler, falls nicht existiert)."""
    shm = shared_memory.SharedMemory(name=POSE_SHM_NAME, create=False)
    arr = np.ndarray((max_persons, num_kpts, 3), dtype=np.float32, buffer=shm.buf)
    return shm, arr
