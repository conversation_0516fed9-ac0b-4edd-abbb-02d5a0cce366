import numpy as np
from multiprocessing import shared_memory

POSE_SHM_NAME = "pose_shm"

def init_pose_shm(max_persons=10, num_kpts=17):
    """Erzeugt /pose_shm neu (vorheriges unlink)."""
    nbytes = max_persons * num_kpts * 3 * np.float32().nbytes
    try:
        old = shared_memory.SharedMemory(name=POSE_SHM_NAME)
        old.unlink(); old.close()
    except FileNotFoundError:
        pass

    shm = shared_memory.SharedMemory(create=True, size=nbytes, name=POSE_SHM_NAME)
    arr = np.ndarray((max_persons, num_kpts, 3), dtype=np.float32, buffer=shm.buf)
    arr.fill(-1.0)
    return shm, arr

def open_pose_shm(max_persons=10, num_kpts=17):
    """Öffnet /pose_shm (<PERSON><PERSON>, falls nicht existiert)."""
    shm = shared_memory.SharedMemory(name=POSE_SHM_NAME, create=False)
    arr = np.ndarray((max_persons, num_kpts, 3), dtype=np.float32, buffer=shm.buf)
    return shm, arr
