Sourcing setup_env.sh from hailo-rpi5-examples...
Setting up the environment...
Setting up the environment for hailo-tappas-core...
TAPPAS_VERSION is 3.31.0. Proceeding...
You are not in the venv_hailo_rpi5_examples virtual environment.
Virtual environment exists. Activating...
TAPPAS_POST_PROC_DIR set to /usr/lib/aarch64-linux-gnu/hailo/tappas/post_processes
HailoRT warning: Cannot create log file hailort.log! Please check the file /home/<USER>/.hailo/hailort/hailort.log write permissions.
[HailoRT] [error] CHECK failed - Failed to open device file /dev/hailo0 with error 2
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT CLI] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
Error: Device Architecture not found. Please check the connection to the device.
[CONFIG] Using video sink: fakesink
[CONFIG] Set GST_AUTOVIDEOSINK=fakesink for performance mode
HailoRT warning: Cannot create log file hailort.log! Please check the file /home/<USER>/.hailo/hailort/hailort.log write permissions.
[HailoRT] [error] CHECK failed - Failed to open device file /dev/hailo0 with error 2
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_DRIVER_OPERATION_FAILED(36)
CHECK_EXPECTED failed with status=36
[0:40:34.523150204] [28721] [1;32m INFO [1;37mCamera [1;34mcamera_manager.cpp:327 [0mlibcamera v0.4.0+53-29156679
[0:40:34.539192942] [28722] [1;32m INFO [1;37mRPI [1;34mpisp.cpp:720 [0mlibpisp version v1.1.0 e7974a156008 27-01-2025 (21:50:51)
[0:40:34.541893238] [28722] [1;31mERROR [1;37mV4L2 [1;34mv4l2_device.cpp:390 [0;32m'imx708_wide_noir': [0mUnable to set controls: Device or resource busy
[0:40:34.542626960] [28722] [1;33m WARN [1;37mCameraSensorProperties [1;34mcamera_sensor_properties.cpp:473 [0mNo static properties available for 'imx708_wide_noir'
[0:40:34.542731645] [28722] [1;33m WARN [1;37mCameraSensorProperties [1;34mcamera_sensor_properties.cpp:475 [0mPlease consider updating the camera sensor properties database
[0:40:34.575167807] [28722] [1;33m WARN [1;37mCameraSensor [1;34mcamera_sensor_legacy.cpp:501 [0;32m'imx708_wide_noir': [0mNo sensor delays found in static properties. Assuming unverified defaults.
[0:40:34.575940918] [28722] [1;32m INFO [1;37mRPI [1;34mpisp.cpp:1179 [0mRegistered camera /base/axi/pcie@120000/rp1/i2c@88000/imx708@1a to CFE device /dev/media2 and ISP device /dev/media0 using PiSP variant BCM2712_C0
[0:40:34.580113806] [28721] [1;32m INFO [1;37mCamera [1;34mcamera.cpp:1008 [0mPipeline handler in use by another process
Camera __init__ sequence did not complete.
Exception in thread Thread-1 (picamera_thread):
Traceback (most recent call last):
  File "/usr/lib/python3/dist-packages/picamera2/picamera2.py", line 269, in __init__
    self._open_camera()
  File "/usr/lib/python3/dist-packages/picamera2/picamera2.py", line 477, in _open_camera
    self.camera.acquire()
RuntimeError: Failed to acquire camera: Device or resource busy

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.11/threading.py", line 1038, in _bootstrap_inner
    self.run()
  File "/usr/lib/python3.11/threading.py", line 975, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/taha_ai/hailo-rpi5-examples/venv_hailo_rpi5_examples/lib/python3.11/site-packages/hailo_apps_infra/gstreamer_app.py", line 273, in picamera_thread
    with Picamera2() as picam2:
         ^^^^^^^^^^^
  File "/usr/lib/python3/dist-packages/picamera2/picamera2.py", line 281, in __init__
    raise RuntimeError("Camera __init__ sequence did not complete.")
RuntimeError: Camera __init__ sequence did not complete.
./run_pose_estimation.sh: line 39: 28676 Segmentation fault      python pose_estimation.py "$@"
/usr/lib/python3.11/multiprocessing/resource_tracker.py:224: UserWarning: resource_tracker: There appear to be 1 leaked shared_memory objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
