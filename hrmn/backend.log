Sourcing setup_env.sh from hailo-rpi5-examples...
Setting up the environment...
Setting up the environment for hailo-tappas-core...
TAPPAS_VERSION is 3.31.0. Proceeding...
You are not in the venv_hailo_rpi5_examples virtual environment.
Virtual environment exists. Activating...
TAPPAS_POST_PROC_DIR set to /usr/lib/aarch64-linux-gnu/hailo/tappas/post_processes
HailoRT warning: Cannot create log file hailort.log! Please check the file /home/<USER>/.hailo/hailort/hailort.log write permissions.
DEVICE_ARCHITECTURE is set to: HAILO8L
[CONFIG] Using video sink: fakesink
[CONFIG] Set GST_AUTOVIDEOSINK=fakesink for performance mode
HailoRT warning: Cannot create log file hailort.log! Please check the file /home/<USER>/.hailo/hailort/hailort.log write permissions.
[HailoRT] [error] CHECK failed - Failed to create vdevice. there are not enough free devices. requested: 1, found: 0
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_OUT_OF_PHYSICAL_DEVICES(74)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_OUT_OF_PHYSICAL_DEVICES(74)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_OUT_OF_PHYSICAL_DEVICES(74)
[HailoRT] [error] CHECK_SUCCESS failed with status=HAILO_OUT_OF_PHYSICAL_DEVICES(74)
CHECK_EXPECTED failed with status=74
[0:51:05.772253596] [29656] [1;32m INFO [1;37mCamera [1;34mcamera_manager.cpp:327 [0mlibcamera v0.4.0+53-29156679
[0:51:05.795281333] [29660] [1;32m INFO [1;37mRPI [1;34mpisp.cpp:720 [0mlibpisp version v1.1.0 e7974a156008 27-01-2025 (21:50:51)
[0:51:05.796397852] [29660] [1;31mERROR [1;37mV4L2 [1;34mv4l2_device.cpp:390 [0;32m'imx708_wide_noir': [0mUnable to set controls: Device or resource busy
[0:51:05.796952315] [29660] [1;33m WARN [1;37mCameraSensorProperties [1;34mcamera_sensor_properties.cpp:473 [0mNo static properties available for 'imx708_wide_noir'
[0:51:05.796992111] [29660] [1;33m WARN [1;37mCameraSensorProperties [1;34mcamera_sensor_properties.cpp:475 [0mPlease consider updating the camera sensor properties database
[0:51:05.816975682] [29660] [1;33m WARN [1;37mCameraSensor [1;34mcamera_sensor_legacy.cpp:501 [0;32m'imx708_wide_noir': [0mNo sensor delays found in static properties. Assuming unverified defaults.
[0:51:05.820731922] [29660] [1;32m INFO [1;37mRPI [1;34mpisp.cpp:1179 [0mRegistered camera /base/axi/pcie@120000/rp1/i2c@88000/imx708@1a to CFE device /dev/media0 and ISP device /dev/media1 using PiSP variant BCM2712_C0
./run_pose_estimation.sh: line 39: 29609 Segmentation fault      python pose_estimation.py "$@"
/usr/lib/python3.11/multiprocessing/resource_tracker.py:224: UserWarning: resource_tracker: There appear to be 1 leaked shared_memory objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
