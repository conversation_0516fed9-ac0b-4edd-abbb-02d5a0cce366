Sourcing setup_env.sh from hailo-rpi5-examples...
Setting up the environment...
Setting up the environment for hailo-tappas-core...
TAPPAS_VERSION is 3.31.0. Proceeding...
You are not in the venv_hailo_rpi5_examples virtual environment.
Virtual environment exists. Activating...
TAPPAS_POST_PROC_DIR set to /usr/lib/aarch64-linux-gnu/hailo/tappas/post_processes
HailoRT warning: Cannot create log file hailort.log! Please check the file /home/<USER>/.hailo/hailort/hailort.log write permissions.
DEVICE_ARCHITECTURE is set to: HAILO8L
[CONFIG] Using video sink: fakesink
[CONFIG] Set GST_AUTOVIDEOSINK=fakesink for performance mode
Hailo<PERSON> warning: Cannot create log file hailort.log! Please check the file /home/<USER>/.hailo/hailort/hailort.log write permissions.
[0:49:32.969383917] [28071] [1;32m INFO [1;37mCamera [1;34mcamera_manager.cpp:327 [0mlibcamera v0.4.0+53-29156679
[0:49:32.991629913] [28080] [1;32m INFO [1;37mRPI [1;34mpisp.cpp:720 [0mlibpisp version v1.1.0 e7974a156008 27-01-2025 (21:50:51)
[0:49:32.992932820] [28080] [1;33m WARN [1;37mCameraSensorProperties [1;34mcamera_sensor_properties.cpp:473 [0mNo static properties available for 'imx708_wide_noir'
[0:49:32.992992580] [28080] [1;33m WARN [1;37mCameraSensorProperties [1;34mcamera_sensor_properties.cpp:475 [0mPlease consider updating the camera sensor properties database
[0:49:33.005947430] [28080] [1;33m WARN [1;37mCameraSensor [1;34mcamera_sensor_legacy.cpp:501 [0;32m'imx708_wide_noir': [0mNo sensor delays found in static properties. Assuming unverified defaults.
[0:49:33.006721485] [28080] [1;32m INFO [1;37mRPI [1;34mpisp.cpp:1179 [0mRegistered camera /base/axi/pcie@120000/rp1/i2c@88000/imx708@1a to CFE device /dev/media0 and ISP device /dev/media1 using PiSP variant BCM2712_C0
[0:49:33.012973095] [28071] [1;32m INFO [1;37mCamera [1;34mcamera.cpp:1202 [0mconfiguring streams: (0) 1280x720-RGB888 (1) 1280x720-RGB888 (2) 1536x864-BGGR_PISP_COMP1
[0:49:33.013212910] [28080] [1;32m INFO [1;37mRPI [1;34mpisp.cpp:1484 [0mSensor: /base/axi/pcie@120000/rp1/i2c@88000/imx708@1a - Selected sensor format: 1536x864-SBGGR10_1X10 - Selected CFE format: 1536x864-PC1B
