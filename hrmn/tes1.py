from flask import Flask

app = Flask(__name__)

# Prompt the user to input a comma-separated list (array) in the terminal
user_input = input("Enter elements of the array, separated by commas (e.g. 1,2,3): ")
# Split the input on commas to create a list
input_array = [item.strip() for item in user_input.split(",")]

@app.route("/")
def display_array():
    # Display the array in red
    return f"<p style='color:red;'>{input_array}</p>"

if __name__ == "__main__":
    # Run Flask app in debug mode; access at http://127.0.0.1:5000
    app.run(debug=True)
