#!/bin/bash

# =============================================================================
# Simple Raspberry Pi Pose Estimation & Hand Ninja Game Startup Script
# =============================================================================

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}[STARTUP]${NC} Starting Raspberry Pi Pose Estimation System"
echo ""

# 1) ULTRA-AGGRESSIVE HARDWARE RESET (fixes "Device or resource busy")
echo -e "${BLUE}[ULTRA-RESET]${NC} Performing complete hardware reset..."

# Kill ALL related processes with extreme prejudice
pkill -9 -f "pose_estimation" 2>/dev/null || true
pkill -9 -f "hand_pose_estimation" 2>/dev/null || true
pkill -9 -f "simple_pose_socket" 2>/dev/null || true
pkill -9 -f "hand_ninja_game" 2>/dev/null || true
pkill -9 -f "picamera" 2>/dev/null || true
pkill -9 -f "libcamera" 2>/dev/null || true
pkill -9 -f "gst-launch" 2>/dev/null || true
pkill -9 -f "multiprocessing.resource_tracker" 2>/dev/null || true

echo -e "${GREEN}[INFO]${NC} All processes terminated"

# ULTRA-AGGRESSIVE camera reset
echo -e "${BLUE}[CAMERA-RESET]${NC} Ultra-aggressive camera reset..."

# Kill ALL camera-related processes
sudo pkill -9 -f "libcamera" 2>/dev/null || true
sudo pkill -9 -f "rpicam" 2>/dev/null || true
sudo pkill -9 -f "picamera" 2>/dev/null || true
sudo pkill -9 -f "v4l2" 2>/dev/null || true

# Stop all camera services
sudo systemctl stop rpicam-apps 2>/dev/null || true
sudo systemctl stop libcamera 2>/dev/null || true

# Remove ALL camera modules
sudo modprobe -r imx708 2>/dev/null || true
sudo modprobe -r bcm2835_v4l2 2>/dev/null || true
sudo modprobe -r bcm2835_mmal_vchiq 2>/dev/null || true
sudo modprobe -r bcm2835_codec 2>/dev/null || true
sudo modprobe -r bcm2835_isp 2>/dev/null || true

# Clear any stuck camera locks
sudo rm -f /tmp/.libcamera* 2>/dev/null || true
sudo rm -f /var/lock/libcamera* 2>/dev/null || true

sleep 3

# Reload camera modules in correct order
sudo modprobe bcm2835_v4l2 2>/dev/null || true
sudo modprobe bcm2835_mmal_vchiq 2>/dev/null || true
sudo modprobe bcm2835_codec 2>/dev/null || true
sudo modprobe bcm2835_isp 2>/dev/null || true
sudo modprobe imx708 2>/dev/null || true

# Restart services
sudo systemctl start rpicam-apps 2>/dev/null || true

echo -e "${GREEN}[INFO]${NC} Ultra-aggressive camera reset completed"

# ULTRA-AGGRESSIVE Hailo device reset (based on community solution)
echo -e "${BLUE}[HAILO-RESET]${NC} Ultra-aggressive Hailo device reset..."

# Kill ALL Hailo-related processes
sudo pkill -9 -f "hailo" 2>/dev/null || true
sudo pkill -9 -f "hailort" 2>/dev/null || true

# Stop all Hailo services
sudo systemctl stop hailort 2>/dev/null || true

# Force remove ALL Hailo modules and clear locks
sudo rmmod hailo_pci 2>/dev/null || true
sudo modprobe -r hailo_pci 2>/dev/null || true

# Clear ALL Hailo device locks and temp files
sudo rm -f /tmp/hailo* 2>/dev/null || true
sudo rm -f /var/lock/hailo* 2>/dev/null || true
sudo rm -f /dev/hailo* 2>/dev/null || true

# Clear system caches to free memory
sudo sh -c 'echo 3 > /proc/sys/vm/drop_caches'

# Wait longer for complete device release
sleep 5

# Reload Hailo modules step by step
echo -e "${BLUE}[HAILO-RELOAD]${NC} Reloading Hailo modules..."
sudo modprobe hailo_pci 2>/dev/null || true
sleep 3

# Restart Hailo service
sudo systemctl start hailort 2>/dev/null || true
sleep 2

# Verify Hailo device is available
if lsmod | grep -q hailo_pci; then
    echo -e "${GREEN}[SUCCESS]${NC} Hailo PCI module loaded successfully"
else
    echo -e "${YELLOW}[WARNING]${NC} Hailo PCI module not loaded - may need reboot"
fi

echo -e "${GREEN}[INFO]${NC} Ultra-aggressive Hailo device reset completed"

# Clean up ALL shared memory and temp files
rm -f /dev/shm/pose_shm 2>/dev/null || true
rm -f /dev/shm/hand_shm 2>/dev/null || true
sudo rm -f /tmp/hailo* 2>/dev/null || true

# Wait for hardware to stabilize
echo -e "${BLUE}[STABILIZE]${NC} Waiting for hardware to stabilize..."
sleep 5

echo -e "${GREEN}[SUCCESS]${NC} Ultra-reset completed - hardware should be free now"
echo ""

# 2) Start backend
echo -e "${BLUE}[BACKEND]${NC} Starting pose estimation backend..."
nohup ./run_pose_estimation.sh --input rpi > backend.log 2>&1 &
BACKEND_PID=$!
echo -e "${GREEN}[INFO]${NC} Backend started with PID: $BACKEND_PID"

# 3) Wait for shared memory with FALLBACK STRATEGY
echo -e "${BLUE}[WAIT]${NC} Waiting for shared memory to be created..."
for i in {1..30}; do
    if [ -f "/dev/shm/pose_shm" ]; then
        echo -e "${GREEN}[SUCCESS]${NC} Shared memory created successfully"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${YELLOW}[FALLBACK]${NC} First attempt failed. Trying more aggressive reset..."

        # FALLBACK: Kill backend and try more aggressive reset
        kill $BACKEND_PID 2>/dev/null || true
        sleep 2

        echo -e "${BLUE}[FALLBACK]${NC} Performing NUCLEAR camera reset..."

        # NUCLEAR RESET - even more aggressive
        sudo pkill -9 -f "gst-launch" 2>/dev/null || true
        sudo pkill -9 -f "hailo" 2>/dev/null || true

        # Reset GPU memory
        sudo sh -c 'echo 3 > /proc/sys/vm/drop_caches'

        # Force unload ALL camera drivers
        sudo rmmod imx708 2>/dev/null || true
        sudo rmmod bcm2835_v4l2 2>/dev/null || true
        sudo rmmod bcm2835_mmal_vchiq 2>/dev/null || true

        sleep 5

        # Reload everything
        sudo modprobe bcm2835_mmal_vchiq
        sudo modprobe bcm2835_v4l2
        sudo modprobe imx708

        sleep 3

        echo -e "${BLUE}[FALLBACK]${NC} Trying RPI camera again after nuclear reset..."
        nohup ./run_pose_estimation.sh --input rpi > backend_fallback.log 2>&1 &
        BACKEND_PID=$!

        # Wait for fallback
        for j in {1..20}; do
            if [ -f "/dev/shm/pose_shm" ]; then
                echo -e "${GREEN}[SUCCESS]${NC} Nuclear reset successful!"
                break
            fi
            if [ $j -eq 20 ]; then
                echo -e "${YELLOW}[ERROR]${NC} Nuclear reset failed. Check logs:"
                echo "Original RPI camera log:"
                tail -5 backend.log
                echo "Nuclear reset log:"
                tail -5 backend_fallback.log
                echo ""
                echo -e "${YELLOW}[CRITICAL]${NC} Camera hardware may be damaged. Try: sudo reboot"
                exit 1
            fi
            sleep 1
            echo -n "."
        done
        break
    fi
    sleep 1
    echo -n "."
done
echo ""

# 4) Start frontend
echo -e "${BLUE}[FRONTEND]${NC} Starting web server..."
nohup python simple_pose_socket.py > frontend.log 2>&1 &
FRONTEND_PID=$!
echo -e "${GREEN}[INFO]${NC} Frontend started with PID: $FRONTEND_PID"

# 5) Wait for frontend to start
sleep 3

# 6) Open browser automatically
echo -e "${BLUE}[BROWSER]${NC} Opening web browser..."
if command -v xdg-open > /dev/null; then
    xdg-open http://localhost:5000 > /dev/null 2>&1 &
    echo -e "${GREEN}[INFO]${NC} Browser opened automatically"
elif command -v chromium-browser > /dev/null; then
    chromium-browser http://localhost:5000 > /dev/null 2>&1 &
    echo -e "${GREEN}[INFO]${NC} Chromium browser opened automatically"
else
    echo -e "${YELLOW}[WARN]${NC} No browser found - please open http://localhost:5000 manually"
fi

# 7) Final status
echo ""
echo -e "${BLUE}[SUCCESS]${NC} Startup Complete!"
echo -e "${GREEN}[INFO]${NC} Backend PID: $BACKEND_PID"
echo -e "${GREEN}[INFO]${NC} Frontend PID: $FRONTEND_PID"
echo -e "${GREEN}[INFO]${NC} Web interface: http://localhost:5000"
echo -e "${GREEN}[INFO]${NC} Logs: backend.log, frontend.log"
echo ""
echo -e "${YELLOW}[TIP]${NC} Use 'pkill -f pose_estimation.py && pkill -f simple_pose_socket.py' to stop"
echo -e "${YELLOW}[TIP]${NC} Use 'tail -f backend.log' or 'tail -f frontend.log' to monitor"
