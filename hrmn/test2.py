#!/usr/bin/env python3
import time
import numpy as np
from shared_memory import init_pose_shm

MAX_PERSONS = 10
NUM_KPTS = 17

# Create/initialize the shared memory block (writer = the "pipeline")
shm, pose_arr = init_pose_shm(MAX_PERSONS, NUM_KPTS)

print("[Writer] Shared memory created. Updating it every 2 seconds...")

try:
    while True:
        # Fill everything with -1
        pose_arr.fill(-1.)

        # Let's pretend we detect 1 person and fill keypoints with random data
        for k in range(NUM_KPTS):
            x = np.random.rand()   # x coordinate
            y = np.random.rand()   # y coordinate
            c = 0.8 + 0.2*np.random.rand()  # confidence ~ 0.8..1.0
            pose_arr[0, k] = (x, y, c)

        print("[Writer] Wrote new random pose for person=0. Next update in 2s...")
        time.sleep(2)

except KeyboardInterrupt:
    print("[Writer] CTRL-C received. Closing shared memory.")
finally:
    shm.close()
    shm.unlink()
