#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Socket.IO server that reads from shared memory (pose_shm) and emits data to clients.
Requires:
  pip install flask flask-socketio eventlet
"""

import eventlet
eventlet.monkey_patch()

from flask import Flask
from flask_socketio import SocketIO

# Import your open_pose_shm function
from shared_memory import open_pose_shm

# Match these with your writer's / pipeline's init_pose_shm settings
MAX_PERSONS = 10
NUM_KPTS = 17

# Attach to the existing shared memory (assumes it's already created by pipeline)
shm, pose_arr = open_pose_shm(MAX_PERSONS, NUM_KPTS)

app = Flask(__name__)
socketio = SocketIO(
    app,
    async_mode="eventlet",
    cors_allowed_origins="*",
    logger=True,
    engineio_logger=True
)

def emit_pose_data():
    """Continuously read from shared memory and emit via Socket.IO."""
    while True:
        # Copy the shared memory array so we don't block it
        snap = pose_arr.copy()

        # Convert to a Python list for JSON serialization
        payload = snap.tolist()

        # Emit the array on the "poseData" event to all connected clients
        socketio.emit("poseData", payload, namespace="/test")

        eventlet.sleep(2.0)  # Adjust frequency as needed

# Start the emitter thread
eventlet.spawn(emit_pose_data)

@app.route("/")
def index():
    """Simple HTML that connects to Socket.IO and logs the poseData events."""
    return """
    <html>
    <head>
        <title>Socket.IO Pose Test</title>
        <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    </head>
    <body>
        <h1>Socket.IO Pose Data Test</h1>
        <div id="status">Connecting...</div>
        <div id="data">No data received yet</div>
        
        <script>
            console.log("Initializing Socket.IO...");

            // Connect to the namespace "/test"
            const socket = io("/test", {
                transports: ["websocket"],
                reconnection: true,
                reconnectionAttempts: 5,
                reconnectionDelay: 1000
            });
            
            socket.on("connect", () => {
                console.log("Socket connected, ID:", socket.id);
                document.getElementById("status").textContent = "Connected";
                document.getElementById("status").style.color = "green";
            });
            
            socket.on("connect_error", (err) => {
                console.error("Connection error:", err);
                document.getElementById("status").textContent = "Connection Error";
                document.getElementById("status").style.color = "red";
            });
            
            socket.on("disconnect", (reason) => {
                console.log("Socket disconnected:", reason);
                document.getElementById("status").textContent = "Disconnected";
                document.getElementById("status").style.color = "red";
            });
            
            // Listen for "poseData" events
            socket.on("poseData", (data) => {
                console.log("Received pose data:", data);
                document.getElementById("data").textContent = 
                    "Pose array: " + JSON.stringify(data);
            });
        </script>
    </body>
    </html>
    """

if __name__ == "__main__":
    print("[Server] Starting Socket.IO pose server on http://0.0.0.0:5000")
    try:
        socketio.run(app, host="0.0.0.0", port=5000, debug=True)
    except Exception as e:
        print(f"[Server] Error starting server: {e}")
