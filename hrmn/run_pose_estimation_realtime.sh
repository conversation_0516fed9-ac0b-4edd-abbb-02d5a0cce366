#!/bin/bash

# ECHTZEIT Pose Estimation - NUR die funktionierenden Optimierungen
# Keine schädlichen Display-Variablen!

# Source the setup_env.sh file to set the TAPPAS_POST_PROC_DIR environment variable
if [ -f "/home/<USER>/Documents/taha_ai/hailo-rpi5-examples/setup_env.sh" ]; then
    echo "Sourcing setup_env.sh from hailo-rpi5-examples..."
    source /home/<USER>/Documents/taha_ai/hailo-rpi5-examples/setup_env.sh
elif [ -f "/home/<USER>/Documents/taha_ai/venv_hailo/setup_env.sh" ]; then
    echo "Sourcing setup_env.sh from venv_hailo..."
    source /home/<USER>/Documents/taha_ai/venv_hailo/setup_env.sh
else
    echo "Error: Could not find setup_env.sh file."
    exit 1
fi

echo "[INFO] Starting REALTIME pose estimation (minimal optimizations for speed)"

# ULTRA-FOKUSSIERT: NUR autovideosink → fakesink
export GST_AUTOVIDEOSINK=fakesink  # Das funktioniert - ersetzt Video-Output
export GST_DEBUG=0                 # Reduziert Debug-Output

# SYSTEMWEITE GSTREAMER-UMLEITUNG:
export GST_PLUGIN_SYSTEM_PATH=/usr/lib/aarch64-linux-gnu/gstreamer-1.0
export GST_REGISTRY_FORK=no
export GST_REGISTRY_UPDATE=no

# KEINE Display-Variablen! Die machen es langsam!
# KEINE Audio-Variablen! Nicht nötig!
# KEINE Hardware-Variablen! Machen Probleme!

# Run with minimal optimizations
python pose_estimation.py --disable-sync "$@"
