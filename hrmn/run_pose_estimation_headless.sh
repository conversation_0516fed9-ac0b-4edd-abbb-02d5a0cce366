#!/bin/bash

# Optimized headless pose estimation without display output
# This version uses DISPLAY= to disable video output for maximum performance

# Source the setup_env.sh file to set the TAPPAS_POST_PROC_DIR environment variable
if [ -f "/home/<USER>/Documents/taha_ai/hailo-rpi5-examples/setup_env.sh" ]; then
    echo "Sourcing setup_env.sh from hailo-rpi5-examples..."
    source /home/<USER>/Documents/taha_ai/hailo-rpi5-examples/setup_env.sh
elif [ -f "/home/<USER>/Documents/taha_ai/venv_hailo/setup_env.sh" ]; then
    echo "Sourcing setup_env.sh from venv_hailo..."
    source /home/<USER>/Documents/taha_ai/venv_hailo/setup_env.sh
else
    echo "Error: Could not find setup_env.sh file."
    exit 1
fi

echo "[INFO] Starting MAXIMUM PERFORMANCE pose estimation (ALL optimizations enabled)"

# OPTION 1: GStreamer Environment Variables
export GST_AUTOVIDEOSINK=fakesink     # Replace autovideosink with fakesink
export GST_AUTOAUDIOSINK=fakesink     # Replace autoaudiosink with fakesink
export GST_DEBUG=0                    # Disable GStreamer debug output
export GST_DEBUG_NO_COLOR=1           # Disable colored debug output
export GST_PLUGIN_SCANNER_TIMEOUT=60  # Reduce plugin scanner timeout

# OPTION 2: Display and Performance Variables
export DISPLAY=                       # Disable X11 display (headless)
export WAYLAND_DISPLAY=               # Disable Wayland display
export QT_QPA_PLATFORM=offscreen     # Qt offscreen platform
export SDL_VIDEODRIVER=dummy         # SDL dummy video driver

# OPTION 3: Hardware Acceleration Optimizations
export LIBVA_DRIVER_NAME=dummy        # Disable VA-API
export VDPAU_DRIVER=dummy             # Disable VDPAU
export GST_GL_PLATFORM=egl            # Use EGL for minimal GL overhead

# Run the pose_estimation.py script with ALL optimizations
python pose_estimation.py --disable-sync "$@"
