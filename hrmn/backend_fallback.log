Sourcing setup_env.sh from hailo-rpi5-examples...
Setting up the environment...
Setting up the environment for hailo-tappas-core...
TAPPAS_VERSION is 3.31.0. Proceeding...
You are not in the venv_hailo_rpi5_examples virtual environment.
Virtual environment exists. Activating...
TAPPAS_POST_PROC_DIR set to /usr/lib/aarch64-linux-gnu/hailo/tappas/post_processes
HailoRT warning: Cannot create log file hailort.log! Please check the file /home/<USER>/.hailo/hailort/hailort.log write permissions.
DEVICE_ARCHITECTURE is set to: HAILO8L
[CONFIG] Using video sink: fakesink
[CONFIG] Set GST_AUTOVIDEOSINK=fakesink for performance mode
[INFO] schreibe Pose-Daten in SHM='pose_shm' - ECHTZEIT OPTIMIERT
[CONFIG] Video sink mode: fakesink
[CONFIG] Applying fakesink monkey-patch for performance mode
[MONKEY-PATCH] GStreamer ElementFactory gepatcht für fakesink!
Provided argument "--input" is set to "usb", however no available USB cameras found. Please connect a camera or specifiy different input method.
/usr/lib/python3.11/multiprocessing/resource_tracker.py:224: UserWarning: resource_tracker: There appear to be 1 leaked shared_memory objects to clean up at shutdown
  warnings.warn('resource_tracker: There appear to be %d '
