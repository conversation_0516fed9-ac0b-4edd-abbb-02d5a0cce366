# 🚀 Raspberry Pi Startup Script

Das `start.sh` Script startet automatisch Backend und Frontend für das Hand Ninja Game.

## 📋 Schnellstart

### Alles starten (Standard):
```bash
./start.sh
```

### Mit verschiedenen Kameras:
```bash
./start.sh --camera-input rpi        # Raspberry Pi Kamera (Standard)
./start.sh --camera-input usb        # USB Kamera (automatisch erkannt)
./start.sh --camera-input /dev/video1 # Spezifische Kamera
```

### Performance-Modi:
```bash
./start.sh --performance    # Fakesink (bessere Performance)
./start.sh --display        # Autovideosink (zeigt Video)
```

### System verwalten:
```bash
./start.sh --stop          # Alle Prozesse stoppen
./start.sh --status        # System-Status anzeigen
./start.sh --no-browser    # Ohne automatisches Browser-Öffnen
./start.sh --no-stop       # Ohne automatisches Cleanup
./start.sh --help          # Hilfe anzeigen
```

## 🎯 Was passiert beim Start?

1. **Stoppt alte Prozesse** - Räumt automatisch auf
2. **Startet Backend** - Pose Estimation mit Kamera
3. **Wartet auf Shared Memory** - Bis Daten verfügbar sind
4. **Startet Frontend** - Web Server für das Spiel
5. **Öffnet Browser** - Automatisch auf http://localhost:5000
6. **Zeigt Status** - PIDs und URLs

## 📊 Logs und Überwachung

### Log-Dateien:
- `backend.log` - Pose Estimation Logs
- `frontend.log` - Web Server Logs

### Status prüfen:
```bash
./start.sh --status
```

### Logs live verfolgen:
```bash
tail -f backend.log    # Backend Logs
tail -f frontend.log   # Frontend Logs
```

## 🔧 Problembehandlung

### Prozesse hängen?
```bash
./start.sh --stop
./start.sh
```

### Kamera funktioniert nicht?
```bash
ls /dev/video*                    # Verfügbare Kameras anzeigen
./start.sh --camera-input usb     # USB Kamera versuchen
```

### Performance-Probleme?
```bash
./start.sh --performance    # Fakesink aktivieren
```

### Shared Memory Probleme?
```bash
ls /dev/shm/                # Shared Memory prüfen
rm -f /dev/shm/pose_shm     # Manuell löschen falls nötig
```

## 🌐 Zugriff

Nach dem Start ist das Spiel verfügbar unter:
- **Lokal**: http://localhost:5000
- **Netzwerk**: http://[RASPBERRY_PI_IP]:5000

## ⚡ Tipps

- **Immer `--stop` verwenden** bevor du neu startest
- **Logs prüfen** bei Problemen
- **Status-Check** mit `--status`
- **Performance-Modus** für bessere FPS auf Raspberry Pi
